<script setup lang="ts">
const { rows = 3 } = defineProps<{
   label: string;
   value?: string;
   multiLine?: boolean;
   rows?: number;
}>();
</script>

<template>
   <UFormGroup
      class="w-full"
      :label
   >
      <UInput
         v-if="!multiLine"
         :model-value="value"
         readonly
         placeholder="No value"
      />
      <UTextarea
         v-else
         :model-value="value"
         readonly
         :rows
         placeholder="No value"
      />
   </UFormGroup>
</template>

<style scoped lang="postcss"></style>
