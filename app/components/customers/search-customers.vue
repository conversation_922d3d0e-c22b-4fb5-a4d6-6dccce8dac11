<script setup lang="ts">
import type { Customer } from '~~/_backend/customers/types';
import { useApiSearchCustomers } from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const modalModel = defineModel('modal', { default: false });

const customerModel = defineModel<Customer | null>('customer', { default: null });

/* ---------------------------------------------------------------------------------------------- */

const query = ref('');
const debouncedQuery = refDebounced(query, 500);

const apiSearch = reactive(useApiSearchCustomers(debouncedQuery));

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'options', label: 'Select' },
   { key: 'customer_name', label: 'Name' },
   { key: 'nic', label: 'NIC' },
   { key: 'phone', label: 'Phone' },
];

const rows = ref<{ [key: string]: any }[]>([]);

watchImmediate(
   () => apiSearch.customers,
   (customers) => {
      rows.value = [];

      customers.forEach((customer) => {
         rows.value.push({
            id: customer.id,
            customer_name: customer.customer_name,
            nic: customer.nic,
            phone: customer.phone,
         });
      });
   },
);

/**
 * Select the customer and then close the modal
 */
function selectCustomer(customerId: number | string) {
   customerModel.value = apiSearch.customers.find(customer => customer.id === customerId) ?? null;
   modalModel.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      v-model="modalModel"
      prevent-close
      :ui="{
         width: 'sm:max-w-screen-lg',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>Search customers</Heading3>
               <div>
                  <LoadingIndicator v-if="apiSearch.status === 'pending'" />
               </div>
            </header>
         </template>

         <section>
            <UInput
               v-model="query"
               placeholder="Enter keywords to search..."
            ></UInput>

            <div class="h-4"></div>

            <UTable
               :columns
               :rows
            >
               <template #options-header="">
                  <div class="w-8"></div>
               </template>

               <template #options-data="{ row }">
                  <UButton
                     variant="soft"
                     icon="i-uil:check"
                     square
                     @click="selectCustomer(row.id)"
                  ></UButton>
               </template>
            </UTable>
         </section>

         <template #footer>
            <footer class="flex w-full justify-end gap-3">
               <UButton
                  color="gray"
                  icon="i-uil:times"
                  @click="modalModel = false"
               >
                  Close
               </UButton>
            </footer>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
