<script setup lang="ts">
import type { Customer } from '~~/_backend/customers/types';
import { useApiSearchCustomers } from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const isSearchModalOpen = ref(false);
const isCreateModalOpen = ref(false);
const isViewModalOpen = ref(false);

const customerModel = defineModel<Customer | null>({ default: null });

/* ---------------------------------------------------------------------------------------------- */

const customerName = computed(() => {
   if (customerModel.value) {
      return customerModel.value.customer_name;
   }
   return '';
});
</script>

<template>
   <UButtonGroup
      class="w-full"
      orientation="horizontal"
   >
      <UButton
         icon="uil:user"
         color="gray"
         :disabled="!customerModel"
         @click="isViewModalOpen = true"
      />
      <UInput
         class="flex-1"
         :model-value="customerName"
         readonly
         placeholder="Choose a customer..."
      />
      <UButton
         icon="i-uil:search-alt"
         @click="isSearchModalOpen = true"
      >
         Search
      </UButton>
      <UButton
         icon="uil:user-plus"
         color="green"
         @click="isCreateModalOpen = true"
      />
   </UButtonGroup>

   <!-- region: Search User -->
   <SearchCustomers
      v-model:customer="customerModel"
      v-model:modal="isSearchModalOpen"
   />
   <!-- endregion: Search User -->

   <!-- region: Add new customer -->
   <CreateCustomer
      v-model:modal="isCreateModalOpen"
      v-model:customer="customerModel"
   />
   <!-- endregion: Add new customer -->

   <!-- region: View customer -->
   <ViewCustomer
      v-model:modal="isViewModalOpen"
      :customer="customerModel"
   />
   <!-- endregion: View customer -->
</template>

<style scoped lang="postcss"></style>
