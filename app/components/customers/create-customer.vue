<script setup lang="ts">
import { z } from 'zod';
import { FetchError } from 'ofetch';
import { type Customer, CustomerSchema } from '~~/_backend/customers/types';

/* ---------------------------------------------------------------------------------------------- */

const modalModel = defineModel('modal', { default: false });

const customerModel = defineModel<Customer | null>('customer', { default: null });

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   customer_name: z.string().min(1, 'Name is required'),
   address: z.string().min(1, 'Address is required'),
   nic: z.string().optional(),
   phone: z.string().optional(),
   email: z.string().email('Email is required').optional().or(z.literal('')),
   since: z.string(),
});

const formState = reactive({
   customer_name: '',
   address: '',
   phone: '',
   nic: '',
   email: '',
   since: todayAsString(),
});

const isCreating = ref(false);
const createErrors = ref('');

/**
 * Handler for creating new customer
 */
async function handleCreateCustomer() {
   console.log('submitting...');

   isCreating.value = true;

   createErrors.value = '';

   try {
      const response = await useNuxtApp().$api('/customer/create', {
         method: 'POST',
         body: formState,
      });

      customerModel.value = CustomerSchema.parse(response);

      modalModel.value = false;
   }
   catch (error) {
      console.error(error);
      if (error instanceof FetchError) {
         console.log(error.data);

         if (error.data.error === 'Customer with this NIC already exists')
            createErrors.value = error.data.error;
      }
      else {
         createErrors.value = 'Failed to create the customer';
      }
   }

   isCreating.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      v-model="modalModel"
      prevent-close
      :ui="{
         width: 'sm:max-w-xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>Create customer</Heading3>
            </header>
         </template>

         <section>
            <!-- region: form -->

            <UForm
               class="flex flex-col gap-5"
               :schema="formSchema"
               :state="formState"
               @submit="handleCreateCustomer"
            >
               <div class="grid grid-cols-2">
                  <UFormGroup
                     label="Date since"
                     name="since"
                  >
                     <InputDate v-model="formState.since" />
                  </UFormGroup>
               </div>

               <div>
                  <UFormGroup
                     label="Customer name"
                     name="customer_name"
                  >
                     <UInput v-model="formState.customer_name" />
                  </UFormGroup>
               </div>

               <div>
                  <UFormGroup
                     label="Address"
                     name="address"
                  >
                     <UTextarea v-model="formState.address" />
                  </UFormGroup>
               </div>

               <div class="grid grid-cols-2 gap-5">
                  <UFormGroup
                     label="Phone"
                     name="phone"
                  >
                     <UInput v-model="formState.phone" />
                  </UFormGroup>

                  <UFormGroup
                     label="NIC"
                     name="nic"
                  >
                     <UInput v-model="formState.nic" />
                  </UFormGroup>
               </div>

               <div>
                  <UFormGroup
                     label="Email"
                     name="email"
                  >
                     <UInput v-model="formState.email" />
                  </UFormGroup>
               </div>

               <AlertError
                  v-if="createErrors"
                  :description="createErrors"
                  class="mb-4"
               />

               <footer class="flex justify-end gap-3">
                  <UButton
                     type="submit"
                     :loading="isCreating"
                  >
                     Create
                  </UButton>

                  <UButton
                     color="gray"
                     icon="i-uil:times"
                     @click="modalModel = false"
                  >
                     Close
                  </UButton>
               </footer>
            </UForm>

            <!-- endregion: form -->
         </section>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
