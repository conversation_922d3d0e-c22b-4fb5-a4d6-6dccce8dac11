<script setup lang="ts">
import dayjs from 'dayjs';
import type { Customer } from '~~/_backend/customers/types';
import ULabel from '~/components/form/u-label.vue';
import { toDateString } from '~/utils/datetime-utils';

/* ---------------------------------------------------------------------------------------------- */

defineProps<{
   customer: Customer | null;
}>();

const modalModel = defineModel('modal', { default: false });

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      v-model="modalModel"
      :ui="{
         width: 'sm:max-w-xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         v-if="customer"
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>{{ customer.customer_name }}</Heading3>
            </header>
         </template>

         <section>
            <div class="flex flex-col gap-5">
               <ULabel
                  multi-line
                  label="Address"
                  :value="customer.address"
               />
               <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                  <ULabel
                     label="Phone"
                     :value="customer.phone"
                  />
                  <ULabel
                     label="Email"
                     :value="customer.email"
                  />
               </div>
               <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                  <ULabel
                     label="NIC"
                     :value="customer.nic"
                  />
               </div>

               <div class="grid grid-cols-2 gap-5">
                  <ULabel label="Customer since" :value="toDateString(dayjs(customer.since))" />
               </div>
            </div>
         </section>

         <template #footer>
            <footer class="flex w-full justify-end gap-3">
               <UButton
                  color="gray"
                  icon="i-uil:times"
                  @click="modalModel = false"
               >
                  Close
               </UButton>
            </footer>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
