<script setup lang="ts">
//
</script>

<template>
   <div class="w-full text-center">
      <span class="loader"></span>
   </div>
</template>

<style scoped lang="postcss">
.loader {
   width: 96px;
   height: 16px;
   display: inline-block;
   background-color: rgba(0, 149, 255, 0.19);
   border: 2px solid #0095ff;
   border-radius: 8px;
   background-image: linear-gradient(45deg, rgb(0, 149, 255) 25%, transparent 25%, transparent 50%, rgb(0, 149, 255) 50%, rgb(0, 149, 255) 75%, transparent 75%, transparent);
   font-size: 30px;
   background-size: 1em 1em;
   box-sizing: border-box;
   animation: barStripe 1s linear infinite;
}

@keyframes barStripe {
   0% {
      background-position: 1em 0;
   }
   100% {
      background-position: 0 0;
   }
}
</style>
