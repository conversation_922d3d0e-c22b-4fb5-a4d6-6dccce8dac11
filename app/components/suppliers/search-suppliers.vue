<script setup lang="ts">
import { useApiSearchSuppliers } from '~~/_backend/suppliers/api';
import type { Supplier } from '~~/_backend/suppliers/types';

/* ---------------------------------------------------------------------------------------------- */

const modalModel = defineModel('modal', { default: false });
const supplierModel = defineModel<Supplier | null>('supplier', { default: null });

/* ---------------------------------------------------------------------------------------------- */

const query = ref('');
const debouncedQuery = refDebounced(query, 500);

const apiSearch = reactive(useApiSearchSuppliers(debouncedQuery));
apiSearch.fetch();

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'supplier_name', label: 'Supplier' },
   { key: 'address', label: 'Address' },
];

const rows = ref<any>([]);

watchImmediate(
   () => apiSearch.suppliers,
   (suppliers) => {
      rows.value = [];

      suppliers.forEach((supplier) => {
         rows.value.push({
            id: supplier.id,
            supplier_name: supplier.supplier_name,
            address: supplier.address,
         });
      });
   },
);

function selectSupplier(supplierId: number) {
   supplierModel.value = apiSearch.suppliers.find(supplier => supplier.id === supplierId) ?? null;
   modalModel.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      v-model="modalModel"
      prevent-close
      :ui="{
         width: 'sm:max-w-xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard>
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>Search suppliers</Heading3>
               <div>
                  <LoadingIndicator v-if="apiSearch.status === 'pending'" />
               </div>
            </header>
         </template>

         <section>
            <UInput
               v-model="query"
               placeholder="Enter keywords to search..."
            ></UInput>

            <div class="h-4"></div>

            <UTable
               :columns
               :rows
            >
               <template #supplier_name-data="{ row }">
                  <div class="flex gap-1 items-center">
                     <UButton
                        variant="soft"
                        icon="i-uil:check"
                        square
                        @click="selectSupplier(row.id)"
                     />
                     <p>{{ row.supplier_name }}</p>
                  </div>
               </template>
            </UTable>
         </section>

         <template #footer>
            <footer class="flex w-full justify-end gap-3">
               <UButton
                  color="gray"
                  icon="i-uil:times"
                  @click="modalModel = false"
               >
                  Close
               </UButton>
            </footer>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
