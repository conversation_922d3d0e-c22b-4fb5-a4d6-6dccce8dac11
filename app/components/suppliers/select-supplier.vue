<script setup lang="ts">
import type { Supplier } from '~~/_backend/suppliers/types';

const isSearchModalOpen = ref(false);
const isCreateModalOpen = ref(false);

const supplierModel = defineModel<Supplier | null>({ default: null });

/* ---------------------------------------------------------------------------------------------- */

const supplierName = computed(() => {
   if (supplierModel.value) {
      return supplierModel.value.supplier_name;
   }
   return '';
});
</script>

<template>
   <UButtonGroup
      class="w-full"
      orientation="horizontal"
   >
      <UInput
         class="flex-1"
         readonly
         placeholder="Choose a supplier"
         :model-value="supplierName"
      ></UInput>

      <UButton
         icon="i-uil:search-alt"
         @click="isSearchModalOpen = true"
      >
         Search
      </UButton>
      <UButton
         icon="i-uil:user-plus"
         color="green"
         @click="isCreateModalOpen = true"
      ></UButton>
   </UButtonGroup>

   <!-- region: Search suppliers -->
   <SearchSuppliers
      v-model:modal="isSearchModalOpen"
      v-model:supplier="supplierModel"
   />
   <!-- endregion: Search suppliers -->

   <!-- region: Create supplier -->
   <CreateSupplier
      v-model:modal="isCreateModalOpen"
      v-model:supplier="supplierModel"
   />
   <!-- endregion: Create supplier -->
</template>

<style scoped lang="postcss"></style>
