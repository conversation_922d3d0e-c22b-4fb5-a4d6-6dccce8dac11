<script setup lang="ts">
interface Props {
   label?: string;
   collapsed?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
   collapsed: true,
   label: 'Debug output',
});

const isCollapsed = ref(props.collapsed ?? false);

const toggleIcon = computed(() => {
   return isCollapsed.value ? 'i-fa6-solid:arrow-down' : 'i-fa6-solid:arrow-up';
});
</script>

<template>
   <div class="my-5 rounded-2xl bg-secondaryColor-50">
      <header
         class="flex items-center gap-2 rounded-2xl bg-secondaryColor-100 p-2"
         @click="isCollapsed = !isCollapsed"
      >
         <UIcon :name="toggleIcon" />

         <h2 class="font-bold">
            {{ label }}
         </h2>
      </header>

      <div
         v-if="!isCollapsed"
         class="overflow-auto p-4"
      >
         <pre><slot></slot></pre>
      </div>
   </div>
</template>

<style scoped lang="postcss"></style>
