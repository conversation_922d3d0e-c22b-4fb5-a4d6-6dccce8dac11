<script setup lang="ts">
import type { DropdownItem } from '#ui/types';

const items: DropdownItem[][] = [
   [
      {
         label: 'Home',
         to: '/',
      },
      {
         label: 'Dashboard',
         to: '/dashboard',
      },
      {
         label: 'User Profile',
         to: '/dashboard/user-profile',
      },
      {
         label: 'Sign in',
         to: '/sign-in',
      },
      {
         label: 'Sign up',
         to: '/sign-up',
      },
   ],
];
</script>

<template>
   <UDropdown :items="items" :popper="{ placement: 'bottom-start' }">
      <UButton variant="link" padded color="white">
         <UIcon name="i-heroicons-chevron-down-20-solid" class="h-6 w-6" />
      </UButton>
   </UDropdown>
</template>
