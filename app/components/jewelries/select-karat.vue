<script setup lang="ts">
const listBoxKaratItems = [
   { value: '9', label: '9 Kt' },
   { value: '10', label: '10 Kt' },
   { value: '11', label: '11Kt' },
   { value: '12', label: '12 Kt' },
   { value: '13', label: '13 Kt' },
   { value: '14', label: '14 Kt' },
   { value: '15', label: '15 Kt' },
   { value: '16', label: '16 Kt' },
   { value: '17', label: '17 Kt' },
   { value: '18', label: '18 Kt' },
   { value: '19', label: '19 Kt' },
   { value: '20', label: '20 Kt' },
   { value: '21', label: '21 Kt' },
   { value: '22', label: '22 Kt' },
   { value: '23', label: '23 Kt' },
   { value: '24', label: '24 Kt' },
   { value: 'BAZAAR', label: 'Bazaar Quality' },
   { value: 'ALL', label: 'All' },
];

const model = defineModel({ default: '22' });
</script>

<template>
   <USelectMenu
      v-model="model"
      :options="listBoxKaratItems"
      value-attribute="value"
   ></USelectMenu>
</template>

<style scoped lang="postcss"></style>
