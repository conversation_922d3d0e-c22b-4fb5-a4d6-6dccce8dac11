<script setup lang="ts">
import { DatePicker } from 'v-calendar';
import 'v-calendar/style.css';
import dayjs from 'dayjs';

/* ---------------------------------------------------------------------------------------------- */

const emits = defineEmits(['update']);

const modelValue = defineModel({
   default: {
      start: new Date(),
      end: new Date(),
   },
});

watch(modelValue, () => {
   emits('update');
});

const disabledDates = [
   {
      start: dayjs().add(1, 'day').toDate(),
      // end: dayjs().add(3, 'day').toDate(),
   },
];

/* ---------------------------------------------------------------------------------------------- */

const isSmallScreen = useMediaQuery('(max-width: 640px)');

const rows = ref(1);
const columns = ref(2);

watch(isSmallScreen, (isSmallScreen) => {
   if (isSmallScreen) {
      rows.value = 2;
      columns.value = 1;
   }
   else {
      rows.value = 1;
      columns.value = 2;
   }
}, { immediate: true });
</script>

<template>
   <DatePicker
      v-model.range="modelValue"
      is-required
      :columns="columns"
      :rows="rows"
      color="blue"
      :disabled-dates="disabledDates"
   />
</template>

<style>

</style>
