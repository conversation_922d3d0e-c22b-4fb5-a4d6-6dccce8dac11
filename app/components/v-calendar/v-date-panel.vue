<script setup lang="ts">
import { DatePicker } from 'v-calendar';
import 'v-calendar/style.css';
import dayjs from 'dayjs';

/* ---------------------------------------------------------------------------------------------- */

const emits = defineEmits(['update']);

const modelValue = defineModel({ default: new Date() });

watch(modelValue, () => {
   emits('update');
});

const disabledDates = [{
   start: dayjs().add(1, 'day').toDate(),
   // end: dayjs().add(3, 'day').toDate(),
}];
</script>

<template>
   <DatePicker
      v-model="modelValue"
      is-required
      color="blue"
      :disabled-dates="disabledDates"
   />
</template>

<style>

</style>
