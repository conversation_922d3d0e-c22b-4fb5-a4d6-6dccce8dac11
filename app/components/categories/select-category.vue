<script setup lang="ts">
import type { Category } from '~~/_backend/categories/types';

const isSearchModalOpen = ref(false);
const isCreateModalOpen = ref(false);

const categoryModel = defineModel<Category | null>({ default: null });

/* ---------------------------------------------------------------------------------------------- */

const categoryTitle = computed(() => {
   if (categoryModel.value) {
      return categoryModel.value.title;
   }
   return '';
});
</script>

<template>
   <UButtonGroup
      class="w-full"
      orientation="horizontal"
   >
      <UInput
         class="flex-1"
         readonly
         placeholder="Choose a category"
         :model-value="categoryTitle"
      ></UInput>
      <UButton
         icon="i-uil:search-alt"
         @click="isSearchModalOpen = true"
      >
         Search
      </UButton>
      <UButton
         icon="i-uil:plus"
         color="green"
         @click="isCreateModalOpen = true"
      ></UButton>
   </UButtonGroup>

   <!-- region: Search categories -->
   <SearchCategories
      v-model:modal="isSearchModalOpen"
      v-model:category="categoryModel"
   />
   <!-- endregion: Search categories -->
</template>

<style scoped lang="postcss"></style>
