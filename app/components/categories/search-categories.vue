<script setup lang="ts">
import { useApiSearchCategories } from '~~/_backend/categories/api';
import type { Category } from '~~/_backend/categories/types';

/* ---------------------------------------------------------------------------------------------- */

const modalModel = defineModel('modal', { default: false });
const categoryModel = defineModel<Category | null>('category', { default: null });

/* ---------------------------------------------------------------------------------------------- */

const query = ref('');
const debouncedQuery = refDebounced(query, 500);

const apiSearch = reactive(useApiSearchCategories(debouncedQuery));

watchImmediate(modalModel, () => {
   apiSearch.fetch();
});

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'title', label: 'Title' },
   { key: 'description', label: 'Description' },
];

const rows = ref<any>([]);

watchImmediate(
   () => apiSearch.categories,
   (categories) => {
      rows.value = [];

      categories.forEach((category) => {
         rows.value.push({
            id: category.id,
            title: category.title,
            description: category.description,
         });
      });
   },
);

function selectCategory(categoryId: number) {
   categoryModel.value
      = apiSearch.categories.find(category => category.id === categoryId) ?? null;
   modalModel.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      v-model="modalModel"
      prevent-close
      :ui="{
         width: 'sm:max-w-xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard>
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>Search categories</Heading3>
               <div>
                  <LoadingIndicator v-if="apiSearch.status === 'pending'" />
               </div>
            </header>
         </template>

         <section>
            <UInput
               v-model="query"
               placeholder="Enter keywords to search..."
            ></UInput>

            <div class="h-4"></div>

            <UTable
               :columns
               :rows
            >
               <template #title-data="{ row }">
                  <div class="flex items-center gap-1">
                     <UButton
                        variant="soft"
                        icon="i-uil:check"
                        square
                        @click="selectCategory(row.id)"
                     />
                     <p>{{ row.title }}</p>
                  </div>
               </template>
            </UTable>
         </section>

         <template #footer>
            <footer class="flex w-full justify-end gap-3">
               <UButton
                  color="gray"
                  icon="i-uil:times"
                  @click="modalModel = false"
               >
                  Close
               </UButton>
            </footer>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
