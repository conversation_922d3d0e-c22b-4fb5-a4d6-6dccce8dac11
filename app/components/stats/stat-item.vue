<script setup lang="ts">
//

interface Props {
   header?: boolean;
   value?: boolean;
   footer?: boolean;
   size?: 'sm' | 'md' | 'lg' | 'xl';
   color?: 'green' | 'blue' | 'red' | 'orange' | 'default';
}

const {
   header = true,
   value = true,
   footer = false,
   size = 'md',
   color = 'default',
} = defineProps<Props>();

const colorClass = computed(() => {
   const colorMap = {
      green: 'text-green-500',
      red: 'text-red-500',
      orange: 'text-orange-500',
      blue: 'text-blue-500',
      default: 'text-black-500',
   };

   return colorMap[color];
});
</script>

<template>
   <div
      class="stat"
      :class="[
         size,
         colorClass,
      ]"
   >
      <header
         v-if="header"
         class="stat-title opacity-60"
      >
         <slot name="title" />
      </header>
      <div
         v-if="value"
         class="stat-value"
      >
         <slot name="value" />
      </div>

      <footer
         v-if="footer"
         class="stat-desc"
      >
         <slot name="footer" />
      </footer>
   </div>
</template>

<style scoped lang="postcss">
.stat {
   @apply inline-grid grid-flow-row border;
   @apply rounded-lg lg:px-6 lg:py-4 px-3 py-2 shadow;

   & .stat-title{
      @apply mb-3 whitespace-nowrap text-end uppercase font-bold
   }

   & .stat-value {
      @apply whitespace-nowrap text-end text-4xl font-extrabold;
   }

   & .stat-desc {
      @apply text-end;
   }

}

.stat.sm {
   & .stat-title {
      @apply text-sm;
   }

   & .stat-value{
      @apply text-2xl;
   }
}
</style>
