<script setup lang="ts">
const { pageCount = 60 } = defineProps<{
   total: number;
   pageCount?: number;
}>();

const modelValue = defineModel<number>({ default: 1 });
</script>

<template>
   <div class="flex w-full justify-center">
      <UPagination
         v-model="modelValue"
         :total
         :page-count
         size="xl"
      />
   </div>
</template>

<style scoped lang="postcss"></style>
