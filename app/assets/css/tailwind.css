@import "@vuepic/vue-datepicker/dist/main.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
   --vc-font-family: '<PERSON>eist', sans-serif;

   /*General*/
   --dp-font-family: 'Geist', sans-serif;
   --dp-border-radius: 6px; /*Configurable border-radius*/
   --dp-cell-border-radius: 4px; /*Specific border radius for the calendar cell*/
   --dp-common-transition: all 0.1s ease-in; /*Generic transition applied on buttons and calendar cells*/

   /*Sizing*/
   --dp-button-height: 35px; /*Size for buttons in overlays*/
   --dp-month-year-row-height: 35px; /*Height of the month-year select row*/
   --dp-month-year-row-button-size: 35px; /*Specific height for the next/previous buttons*/
   --dp-button-icon-height: 20px; /*Icon sizing in buttons*/
   --dp-cell-size: 35px; /*Width and height of calendar cell*/
   --dp-cell-padding: 5px; /*Padding in the cell*/
   --dp-common-padding: 10px; /*Common padding used*/
   --dp-input-icon-padding: 35px; /*Padding on the left side of the input if icon is present*/
   --dp-input-padding: 6px 30px 6px 12px; /*Padding in the input*/
   --dp-menu-min-width: 260px; /*Adjust the min width of the menu*/
   --dp-action-buttons-padding: 2px 5px; /*Adjust padding for the action buttons in action row*/
   --dp-row-margin: 5px 0; /*Adjust the spacing between rows in the calendar*/
   --dp-calendar-header-cell-padding: 0.5rem; /*Adjust padding in calendar header cells*/
   --dp-two-calendars-spacing: 10px; /*Space between multiple calendars*/
   --dp-overlay-col-padding: 3px; /*Padding in the overlay column*/
   --dp-time-inc-dec-button-size: 32px; /*Sizing for arrow buttons in the time picker*/
   --dp-menu-padding: 6px 8px; /*Menu padding*/

   /*Font sizes*/
   --dp-font-size: 0.875rem; /*Default font-size*/
   --dp-preview-font-size: 0.8rem; /*Font size of the date preview in the action row*/
   --dp-time-font-size: 0.8rem; /*Font size in the time picker*/

   /*Transitions*/
   --dp-animation-duration: 0.1s; /*Transition duration*/
   --dp-menu-appear-transition-timing: cubic-bezier(
      0.4,
      0,
      1,
      1
   ); /*Timing on menu appear animation*/
   --dp-transition-timing: ease-out; /*Timing on slide animations*/
}

html {
   /*font-size: 14px;*/
   color: theme("colors.black");
   scroll-behavior: smooth;
}

/* ---------------------------------------------------------------------------------------------- */

.heading1{
   @apply text-4xl font-bold;
}

/* ---------------------------------------------------------------------------------------------- */


.bill-footer {
   @apply mt-10;
   @apply flex flex-col justify-end md:flex-row;

   & section {
      @apply text-end inline-grid grid-flow-row border;
      @apply rounded-lg lg:px-6 lg:py-4 px-3 py-2 shadow;

      & h2 {
         @apply text-2xl font-bold;
      }

      & p {
         @apply text-xl font-bold text-secondaryColor-500;
      }
   }
}


/* ---------------------------------------------------------------------------------------------- */





/*!* scrollbar width *!*/
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/*!* scrollbar Track *!*/
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/*!* scrollbar Handle *!*/
::-webkit-scrollbar-thumb {
  background: rgba(85, 85, 85, 0.3);
  border-radius: 10px;
}

/* remove the  input type number changing arrows */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}


