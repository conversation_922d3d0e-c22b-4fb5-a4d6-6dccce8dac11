export default defineNuxtPlugin(async (nuxtApp) => {
   const { session, clear } = useUserSession();

   const baseURL = getApiBaseURL();

   const api = $fetch.create({
      baseURL,

      onRequest({ options }) {
         if (session.value?.token) {
            const headers = options.headers ||= {};
            if (Array.isArray(headers)) {
               headers.push(['Authorization', `Bearer ${session.value?.token}`]);
            }
            else if (headers instanceof Headers) {
               headers.set('Authorization', `Bearer ${session.value?.token}`);
            }
            else {
               headers.Authorization = `Bearer ${session.value?.token}`;
            }
         }
      },

      async onResponseError({ response }) {
         if (response.status === 401) {
            await clear();
            await nuxtApp.runWithContext(() => navigateTo('/login'));
         }
      },
   });

   return {
      provide: {
         api,
      },
   };
});
