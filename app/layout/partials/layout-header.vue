<script setup lang="ts">
import UserWidget from '~/layout/partials/user-widget.vue';

defineEmits(['showSidebar']);
</script>

<template>
   <header class="flex w-full">
      <div
         class="flex w-full items-center justify-between p-4 border-b border-b-primaryColor-100"
      >
         <div class="flex items-center gap-5">
            <div class="">
               <UButton variant="link" class="lg:hidden" @click="$emit('showSidebar')">
                  <UIcon name="i-heroicons-bars-3" class="size-8 text-black" />
               </UButton>
            </div>

            <div>
               <!-- region: placeholder -->

               <!-- endregion: placeholder -->
            </div>
         </div>

         <div class="flex items-center gap-2">
            <div>
               <UserWidget />
            </div>
         </div>
      </div>
   </header>
</template>

<style scoped lang="postcss">

</style>
