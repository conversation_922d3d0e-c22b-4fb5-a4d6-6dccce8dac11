<script setup lang="ts">
const { user } = useUserSession();

const isAdmin = computed(() => {
   return user.value?.role === 'ADMIN';
});
</script>

<template>
   <section class="sidebar-group">
      <header>Home</header>

      <div class="buttons">
         <UButton
            label="Dashboard"
            icon="i-uil:create-dashboard"
            color="green"
            to="/"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>Billing</header>

      <div class="buttons">
         <UButton
            v-if="isAdmin"
            label="Bill stats"
            icon="i-uil:bullseye"
            color="blue"
            to="/bills/stats"
         />
         <UButton
            label="Bills"
            icon="i-uil:receipt-alt"
            color="blue"
            to="/bills"
         />
         <UButton
            label="Create bill"
            icon="i-uil:plus"
            color="blue"
            to="/bills/new"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>Pawning</header>

      <div class="buttons">
         <UButton
            label="Pawns stats"
            icon="i-uil:bullseye"
            color="purple"
            to="/pawns/stats"
         />
         <UButton
            label="Pawns"
            icon="i-uil:receipt-alt"
            color="purple"
            to="/pawns"
         />
         <UButton
            label="Create pawn"
            icon="i-uil:plus"
            color="purple"
            to="/pawns/new"
         />
         <UButton
            label="Today's pawns"
            icon="i-uil:calendar-alt"
            color="purple"
            to="/pawns/today"
         />
         <UButton
            label="Unmarked pawns"
            icon="i-uil:folder-check"
            color="purple"
            to="/pawns/unmarked"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>Jewelleries</header>

      <div class="buttons">
         <UButton
            label="Jewelries"
            icon="i-uil:receipt-alt"
            color="lime"
            to="/jewelries"
         />
         <UButton
            label="Create jewelry"
            icon="i-uil:plus"
            color="lime"
            to="/jewelries/new"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>Ledger</header>

      <div class="buttons">
         <UButton
            label="Ledger"
            icon="i-uil:books"
            color="violet"
            to="/ledger"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>Associates</header>

      <div class="buttons">
         <UButton
            label="Categories"
            icon="i-uil:tag-alt"
            color="sky"
            to="/categories"
         />
         <UButton
            label="Suppliers"
            icon="i-uil:house-user"
            color="sky"
            to="/suppliers"
         />
         <UButton
            label="Customers"
            icon="i-uil:users-alt"
            color="sky"
            to="/customers"
         />
      </div>
   </section>

   <section class="sidebar-group">
      <header>System</header>

      <div class="buttons">
         <UButton
            label="All users"
            icon="i-uil:user-arrows"
            color="amber"
         />
         <UButton
            label="Create user"
            icon="i-uil:user-plus"
            color="amber"
         />
      </div>
   </section>
</template>

<style scoped lang="postcss">
.sidebar-group {
   @apply mb-5;
}

.sidebar-group header {
   @apply text-xs font-bold uppercase text-secondaryColor-500;
   @apply mb-2 border-b border-b-primaryColor-200 p-1;
}

.sidebar-group .buttons {
   @apply grid grid-cols-1 gap-1;
}
</style>
