<script setup lang="ts">
import type { DropdownItem, VerticalNavigationLink } from '#ui/types';

const { user, clear } = useUserSession();

const { apiUrl } = useRuntimeConfig().public;

const profilePicUrl = computed(
   () => `${apiUrl}/uploads/images/profiles/${user.value?.profilePicture}`,
);

/* ---------------------------------------------------------------------------------------------- */

async function logout() {
   await clear();
   return navigateTo('/login');
}

/* ---------------------------------------------------------------------------------------------- */

const dropdownItems: VerticalNavigationLink[][] = [
   [
      {
         label: 'User profile',
         icon: 'i-uil:user',
      },
   ],
   [
      {
         label: 'Logout',
         icon: 'i-uil:signout',
         click: () => logout(),
      },
   ],
];

// adding admin only options, if the authenticated user is an ADMIN
if (user.value?.role === 'ADMIN') {
   dropdownItems[0]?.splice(1, 0, { label: 'Manage users', icon: 'i-uil:user' });
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UPopover>
      <div class="flex items-center gap-3">
         <UAvatar
            :src="profilePicUrl"
            size="md"
         />
      </div>

      <template #panel>
         <div class="">
            <header class="mb-2 border-b bg-primaryColor-100 p-4">
               <p class="text-xl font-bold text-primaryColor-500">
                  {{ user?.fullName }}
               </p>
               <p class="text-primaryColor-400">
                  {{ user?.email }}
               </p>
            </header>

            <section class="p-1">
               <UVerticalNavigation :links="dropdownItems" />
            </section>
         </div>
      </template>
   </UPopover>
</template>

<style scoped lang="postcss"></style>
