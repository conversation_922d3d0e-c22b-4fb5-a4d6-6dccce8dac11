<script setup lang="ts">
import LayoutHeader from '~/layout/partials/layout-header.vue';

/* ---------------------------------------------------------------------------------------------- */

const showSidebar = ref(false);

/* ---------------------------------------------------------------------------------------------- */

function onShowSidebar() {
   showSidebar.value = true;
}

/* ---------------------------------------------------------------------------------------------- */

const appConfig = useAppConfig();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div class="flex h-screen bg-secondaryColor-100">
      <section
         class="absolute z-20 flex h-screen w-[275px] shrink-0 transform bg-primaryColor-50 shadow duration-75 ease-in lg:relative"
         :class="[
            showSidebar
               ? 'translate-x-0 shadow-2xl lg:shadow-none'
               : '-translate-x-full lg:translate-x-0',
         ]"
      >
         <aside class="relative flex h-full w-full flex-col">
            <section class="flex flex-col p-4 md:p-6">
               <h1 class="text-3xl font-bold text-center uppercase text-secondaryColor-500">
                  Abira Jewellery
               </h1>
               <p class="text-xs uppercase text-center">
                  Version {{ appConfig.app.version }}
               </p>
            </section>

            <section class="flex-1 overflow-auto px-4 pb-4 md:px-6 md:pb-6">
               <LayoutSidebar />
            </section>
         </aside>
      </section>

      <!--  -->

      <div
         v-if="showSidebar"
         class="fixed z-10 h-screen w-screen bg-white/50 backdrop-blur"
         @click="showSidebar = false"
      ></div>

      <!-- start: main area -->
      <main class="bg-white flex-1 w-full overflow-auto">
         <div class="">
            <!-- start: header for <lg screens -->
            <LayoutHeader @show-sidebar="onShowSidebar" />
            <!-- end: header for <lg screens -->

            <!--  -->

            <div class="h-full w-full p-4 md:p-6">
               <div class="container mx-auto">
                  <slot />
               </div>
            </div>
         </div>
      </main>
      <!-- end: main area -->
   </div>
</template>

<style scoped lang="postcss"></style>
