<script setup lang="ts">
import { z } from 'zod';
import { useApiFetchCategory } from '~~/_backend/categories/api';

/* ---------------------------------------------------------------------------------------------- */

definePageMeta({
   middleware: ['admin-only'],
});

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;
const categoryId = ref(Number(id));

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch category info
 */

const apiFetch = reactive(useApiFetchCategory(categoryId));

await apiFetch.fetch();

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   title: z.string().min(2, 'Title is required'),
});

const formState = reactive({
   id: categoryId.value,
   title: apiFetch.category?.title ?? '',
   description: apiFetch.category?.description ?? '',
});

const isSubmitting = ref(false);

/**
 * Handles form submit
 */
async function handleUpdate() {
   isSubmitting.value = true;

   try {
      await useNuxtApp().$api<{ id: number }>('/stock-category/update', {
         method: 'PATCH',
         body: formState,
      });

      return navigateTo(`/categories/${id}`);
   }
   catch (error) {
      console.error(error);
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
/*
 * Delete the category
 */

const confirmDelete = ref(false);

const deleteErrors = ref('');
const isDeleting = ref(false);

async function handleDelete() {
   isDeleting.value = true;

   try {
      await useNuxtApp().$api('/stock-category/delete', {
         method: 'DELETE',
         body: {
            id: categoryId.value,
         },
      });

      return navigateTo('/categories');
   }
   catch (e) {
      console.error(e);
      deleteErrors.value = 'Failed to delete the cagegory';
   }

   isDeleting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
/*
 * Path: /categories/{id}/edit
 * Description: List all categories
 */

useAppTitle('Update category');

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge
         v-if="apiFetch.category"
         class="mx-auto max-w-2xl"
      >
         <!-- region: Edit category -->
         <div>
            <!-- region: Header -->
            <header>
               <Heading1>Update {{ apiFetch.category.title }}</Heading1>
            </header>
            <!-- endregion: Header -->

            <UForm
               class="flex flex-col gap-5"
               :state="formState"
               :schema="formSchema"
               @submit="handleUpdate()"
            >
               <div>
                  <UFormGroup
                     label="Title"
                     name="title"
                  >
                     <UInput v-model="formState.title" />
                  </UFormGroup>
               </div>

               <div>
                  <UFormGroup
                     label="Description"
                     name="description"
                  >
                     <UTextarea
                        v-model="formState.description"
                        placeholder="Providing a meaningful description helps identify the associated products better"
                     />
                  </UFormGroup>
               </div>

               <footer class="flex justify-end gap-3">
                  <UButton type="submit">
                     Update
                  </UButton>
                  <UButton
                     color="gray"
                     :to="`/categories/${id}`"
                  >
                     Cancel
                  </UButton>
               </footer>
            </UForm>
         </div>
         <!-- region: Edit category -->

         <!-- region: Delete category -->
         <div class="mt-10">
            <UDivider class="mb-5" />

            <!-- region: Header -->
            <header class="">
               <Heading2>Delete</Heading2>
               <p class="mb-2 text-red-400">
                  Delete option is permanent and cannot be restored. Check 'Confirm' to activate
                  delete option.
               </p>

               <div class="flex items-center justify-between gap-2">
                  <UCheckbox
                     v-model="confirmDelete"
                     label="Confirm delete"
                  ></UCheckbox>

                  <UButton
                     color="red"
                     :disabled="!confirmDelete"
                     :loading="isDeleting"
                     @click="handleDelete()"
                  >
                     Delete this category
                  </UButton>
               </div>
            </header>
            <!-- endregion: Header -->
         </div>
         <!-- region: Delete category -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
