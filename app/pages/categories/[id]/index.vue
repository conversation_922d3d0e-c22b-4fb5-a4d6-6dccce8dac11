<script setup lang="ts">
import { formatLKR } from '~/utils/formatting-utils';
import { useApiFetchCategory, useApiFetchCategoryJewelries } from '~~/_backend/categories/api';
import type { FetchCategoryJewelriesParams } from '~~/_backend/categories/types';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /categories/[id]
 * Description: View category details
 */

useAppTitle('Category');

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;
const categoryId = ref(Number(id));

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch category info
 */

const apiFetch = reactive(useApiFetchCategory(categoryId));

apiFetch.fetch();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch jewelries in the category
 */

const categoryJewelriesQueryParams = reactive({
   category_id: categoryId.value,
   page: 1,
   karat: 'ALL',
   location: 'ALL',
   sold: 'ALL',
   weight_max: 0,
   weight_min: 0,
   query: '',
} as FetchCategoryJewelriesParams);

const apiCategoryJewelries = reactive(useApiFetchCategoryJewelries(categoryJewelriesQueryParams));

apiCategoryJewelries.fetch();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: Category -->
      <section
         v-if="apiFetch.category"
         class="mb-10"
      >
         <div class="flex flex-col justify-between lg:flex-row">
            <div>
               <Heading1 class="flex items-center gap-2">
                  <UIcon name="i-fa6-solid:tag" />
                  {{ apiFetch.category.title }}
               </Heading1>
               <p v-if="apiFetch.category.description">
                  {{ apiFetch.category.description }}
               </p>
               <p
                  v-else
                  class="text-secondaryColor-400"
               >
                  No category description provided
               </p>
            </div>

            <div>
               <UButton
                  icon="i-uil:edit-alt"
                  color="amber"
                  :to="`/categories/${apiFetch.category.id}/edit`"
               >
                  Edit Category
               </UButton>
            </div>
         </div>
      </section>
      <!-- endregion: Category -->

      <!-- region: Jewelries -->
      <section>
         <JewelryListingsTable :items="apiCategoryJewelries.jewelries" />
      </section>
      <!-- endregion: Jewelries -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
