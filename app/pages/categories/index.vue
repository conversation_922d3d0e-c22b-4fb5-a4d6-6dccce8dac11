<script setup lang="ts">
import { useApiFetchCategories } from '~~/_backend/categories/api';
import { formatLKR, formatWeight } from '~/utils/formatting-utils';
import CategoryListingsToolbar from '~/page-components/categories/category-listings-toolbar.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /categories
 * Description: List all categories
 */

useAppTitle('Categories');

/* ---------------------------------------------------------------------------------------------- */

const txtQuery = ref('');

const queryParams = reactive({
   page: 1,
   query: '',
});

const apiFetchCategories = reactive(useApiFetchCategories(queryParams));

apiFetchCategories.fetch();

watchDebounced(txtQuery, (query) => {
   queryParams.query = query;
}, { debounce: 500 });

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   {
      key: 'title',
      label: 'Title',
   },
   {
      key: 'description',
      label: 'Description',
   },
   {
      key: 'jewelries_count',
      label: 'Item count',
   },
   {
      key: 'jewelries_sum_cost',
      label: 'Cost',
   },
   {
      key: 'jewelries_sum_weight',
      label: 'Weight',
   },
];
</script>

<template>
   <AppLayout>
      <Heading1>Jewelry Categories</Heading1>

      <!-- region: toolbar -->
      <div class="my-5">
         <CategoryListingsToolbar v-model="queryParams" @create="apiFetchCategories.fetch()" />
      </div>
      <!-- endregion: toolbar -->

      <div v-if="apiFetchCategories.paginatedCategories">
         <div class="rounded-2xl border mb-5">
            <UTable
               :rows="apiFetchCategories.paginatedCategories.data"
               :columns
            >
               <!-- region: Headers -->
               <template #jewelries_count-header="{ column }">
                  <div class="text-right">
                     {{ column.label }}
                  </div>
               </template>

               <template #jewelries_sum_cost-header="{ column }">
                  <div class="text-right">
                     {{ column.label }}
                  </div>
               </template>

               <template #jewelries_sum_weight-header="{ column }">
                  <div class="text-right">
                     {{ column.label }}
                  </div>
               </template>
               <!-- endregion: : Headers -->

               <!-- region: Rows -->

               <template #title-data="{ row }">
                  <UButton variant="soft" class="uppercase" :to="`/categories/${row.id}`">
                     {{ row.title }}
                  </UButton>
               </template>

               <template #description-data="{ row }">
                  <div
                     v-if="row.description !== ''"
                     class="text-secondaryColor-500"
                  >
                     {{ row.description }}
                  </div>
                  <div v-else class="text-secondaryColor-300">
                     No description
                  </div>
               </template>

               <template #jewelries_count-data="{ row }">
                  <div class="text-right">
                     {{ row.jewelries_count }}
                  </div>
               </template>

               <template #jewelries_sum_cost-data="{ row }">
                  <div class="text-right">
                     {{ formatLKR(row.jewelries_sum_cost) }}
                  </div>
               </template>

               <template #jewelries_sum_weight-data="{ row }">
                  <div
                     v-if="row.jewelries_sum_weight"
                     class="text-right"
                  >
                     {{ formatWeight(row.jewelries_sum_weight) }}
                  </div>
                  <div
                     v-else
                     class="text-right"
                  >
                     0 g
                  </div>
               </template>

               <!-- endregion: Rows -->
            </UTable>
         </div>

         <footer>
            <p>
               There are {{ apiFetchCategories.paginatedCategories.total }} categories
            </p>
         </footer>

         <footer>
            <PaginationContainer
               v-model="queryParams.page"
               :total="apiFetchCategories.paginatedCategories.total"
               :page-count="apiFetchCategories.paginatedCategories.per_page"
            />
         </footer>
      </div>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
