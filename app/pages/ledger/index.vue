<script setup lang="ts">
import { type LedgerEntryQueryParams, useApiFetchLedgerEntries } from '~~/_backend/ledger/api';

/* ---------------------------------------------------------------------------------------------- */

const router = useRouter();
const route = useRoute();

/* ---------------------------------------------------------------------------------------------- */

const entryDateRange = ref([todayAsString(), todayAsString()]);
const queryRef = ref('');

const query = debouncedRef(queryRef, 1000);

/* ---------------------------------------------------------------------------------------------- */

const queryParams = reactive({
   entry_date_max: todayAsString(),
   entry_date_min: todayAsString(),
   query: '',
} as LedgerEntryQueryParams);

const apiFetch = reactive(useApiFetchLedgerEntries(queryParams));

apiFetch.fetch();

/* ---------------------------------------------------------------------------------------------- */

watchDeep(entryDateRange, ([min, max]) => {
   if (!min || !max) {
      return;
   }

   queryParams.entry_date_min = min;
   queryParams.entry_date_max = max;

   apiFetch.fetch();

   router.push({
      query: { ...queryParams },
   });
});

watch(query, query => (queryParams.query = query));

/* ---------------------------------------------------------------------------------------------- */

/*
 * Init query params from url data
 */

const urlQuery = route.query;

queryParams.query = String(urlQuery.query ?? '');
queryParams.entry_date_min = String(urlQuery.entry_date_min ?? todayAsString());
queryParams.entry_date_max = String(urlQuery.entry_date_max ?? todayAsString());

if (queryParams.entry_date_min !== '' && queryParams.entry_date_max !== '') {
   entryDateRange.value = [queryParams.entry_date_min, queryParams.entry_date_max];
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: Heading -->
      <header>
         <Heading1>Ledger entries</Heading1>
      </header>
      <!-- endregion: Heading -->

      <!-- region: toolbar -->
      <section class="mb-10">
         <div class="flex flex-col gap-3 rounded-xl bg-primaryColor-50 p-2 lg:flex-row">
            <div>
               <UInput
                  v-model="queryRef"
                  placeholder="Search"
               />
            </div>

            <div>
               <InputDateRange v-model="entryDateRange" />
            </div>

            <div class="ml-auto">
               <AddLedgerEntry @create="apiFetch.fetch()" />
            </div>
         </div>
      </section>
      <!-- endregion: toolbar -->

      <!-- region: table -->
      <section>
         <LedgerEntriesTable
            :entries="apiFetch.ledgerEntries"
            @update="apiFetch.fetch()"
         />
      </section>
      <!-- endregion: table -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
