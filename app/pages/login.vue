<script setup lang="ts">
import { z } from 'zod';
import { FetchError } from 'ofetch';
import { useFetchHel<PERSON> } from '~/composables/use-fetch-helpers';

/* ---------------------------------------------------------------------------------------------- */

const { loggedIn } = useUserSession();

if (loggedIn.value) {
   navigateTo('/');
}

/* ---------------------------------------------------------------------------------------------- */

const loginFormSchema = z.object({
   email: z.string().min(1, 'Required').email('Should be a valid email address'),
   password: z.string().min(1, 'Required'),
});

const loginFormData = reactive({
   email: '',
   password: '',
});

/* ---------------------------------------------------------------------------------------------- */

const { isSubmitting, errorMessage } = useFetchHelpers();

/**
 * Handles @submit
 */
async function handleLogin() {
   isSubmitting.value = true;

   try {
      await $fetch('/api/auth/login', {
         method: 'POST',
         body: {
            email: loginFormData.email,
            password: loginFormData.password,
         },
      });

      const { fetch } = useUserSession();
      await fetch();
      /**
       * Successfully logged in, redirecting to home page
       */
      return navigateTo('/');
   }
   catch (e) {
      if (e instanceof FetchError) {
         errorMessage.value = e.data.message;
      }
   }

   isSubmitting.value = false;
}
</script>

<template>
   <div class="flex h-screen w-screen items-center justify-center bg-secondaryColor-100">
      <section class="w-[32rem] rounded-xl bg-white p-5 shadow-[-14px_-6px_40px_48px_#77237105]">
         <header class="mb-5">
            <h1 class="text-4xl font-bold text-primaryColor-500">
               Abira Jewellery
            </h1>
            <p class="text-secondaryColor-400">
               Login to continue
            </p>
         </header>

         <!-- region: Login form -->
         <UForm
            class="flex flex-col gap-5"
            :schema="loginFormSchema"
            :state="loginFormData"
            @submit="handleLogin"
         >
            <UFormGroup
               label="Email"
               name="email"
               required
            >
               <UInput v-model="loginFormData.email" />
            </UFormGroup>

            <UFormGroup
               label="Password"
               name="password"
               required
            >
               <UInput
                  v-model="loginFormData.password"
                  type="password"
               />
            </UFormGroup>

            <!-- region: Error -->
            <AlertError
               v-if="errorMessage"
               title="Login failed"
               :description="errorMessage"
            ></AlertError>
            <!-- endregion: Error -->

            <!-- region: Footer -->
            <div class="flex w-full justify-end gap-5">
               <UButton
                  size="xl"
                  type="submit"
                  :loading="isSubmitting"
               >
                  Login to continue
               </UButton>

               <UButton
                  size="xl"
                  color="gray"
               >
                  Cancel
               </UButton>
            </div>
            <!-- endregion: Footer -->
         </UForm>
         <!-- endregion: Login form -->
      </section>
   </div>
</template>

<style scoped lang="postcss"></style>
