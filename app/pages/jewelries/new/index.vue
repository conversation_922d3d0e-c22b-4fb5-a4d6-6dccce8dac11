<script setup lang="ts">
import { z } from 'zod';
import SelectSupplier from '~/components/suppliers/select-supplier.vue';
import SelectCategory from '~/components/categories/select-category.vue';
import type { Supplier } from '~~/_backend/suppliers/types';
import type { Category } from '~~/_backend/categories/types';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /jewelry/[id]
 * Description: Single jewelry
 */

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

const isAdmin = computed(() => {
   if (user) {
      return user.value.isAdmin;
   }
   return false;
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * Create jewelry
 */

const selectedSupplier = ref<Supplier | null>(null);
const selectedCategory = ref<Category | null>(null);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z
   .object({
      stock_id: z.string(),
      purchased_date: z.string().date('Should be a valid date'),
      title: z.string().min(1, 'Title is required'),
      cost: z.coerce.number().positive('Needs to be a valid amount'),
      price: z.coerce.number().positive('Needs to be a valid amount'),
   })
   .refine(
      (data) => {
         return data.price >= data.cost;
      },
      {
         message: 'Price should be greater than the cost',
         path: ['price'],
      },
   );

const formState = reactive({
   stock_id: '',
   purchased_date: todayAsString(),
   category_id: 0,
   supplier_id: 0,
   title: '',
   description: '',
   karat: '22',
   weight: 0,
   cost: 0,
   price: 0,
   location: 'DISPLAY',
});

/* ---------------------------------------------------------------------------------------------- */

const isSubmitting = ref(false);
const submitErrors = ref('');

async function handleSubmit() {
   submitErrors.value = '';

   if (formState.category_id === 0) {
      submitErrors.value = 'Please choose a category';
      return;
   }
   if (formState.supplier_id === 0) {
      submitErrors.value = 'Please choose a supplier';
      return;
   }

   isSubmitting.value = true;

   try {
      const response = await useNuxtApp().$api<{ id: number }>('/jewelry/create', {
         method: 'POST',
         body: formState,
      });

      return navigateTo(`/jewelries/${response.id}`);
   }
   catch (error) {
      console.error(error);
      submitErrors.value = 'Failed to save the jewelry';
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */

watchImmediate(selectedSupplier, (suppler) => {
   if (!suppler)
      return;
   formState.supplier_id = suppler.id;
});

watchImmediate(selectedCategory, (category) => {
   if (!category)
      return;
   formState.category_id = category.id;
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <div class="mx-auto max-w-3xl">
            <Heading1>Create new jewelry</Heading1>

            <UForm
               class="flex w-full flex-col gap-5"
               :schema="formSchema"
               :state="formState"
               @submit="handleSubmit"
            >
               <!-- region: form -->
               <div class="grid lg:grid-cols-3">
                  <UFormGroup
                     label="Purchased date"
                     name="purchased_date"
                  >
                     <InputDate
                        v-model="formState.purchased_date"
                        :deny-past="!isAdmin"
                     />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-2">
                  <UFormGroup label="Supplier">
                     <SelectSupplier v-model="selectedSupplier" />
                  </UFormGroup>

                  <UFormGroup label="Category">
                     <SelectCategory v-model="selectedCategory" />
                  </UFormGroup>
               </div>

               <div class="grid lg:grid-cols-3">
                  <UFormGroup
                     label="Stock ID"
                     description="Optional, If not provided, ID will be generated automatically."
                  >
                     <UInput v-model="formState.stock_id" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup
                     label="Title"
                     required
                     name="title"
                  >
                     <UInput v-model="formState.title" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup label="Description">
                     <UTextarea v-model="formState.description" />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-4">
                  <UFormGroup
                     label="Karat"
                     required
                  >
                     <SelectKarat v-model="formState.karat" />
                  </UFormGroup>

                  <UFormGroup
                     label="Weight"
                     required
                  >
                     <UInput v-model="formState.weight" />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-2">
                  <UFormGroup
                     label="Cost"
                     required
                     name="cost"
                  >
                     <UInput v-model="formState.cost" />
                  </UFormGroup>

                  <UFormGroup
                     label="Price"
                     required
                     name="price"
                  >
                     <UInput v-model="formState.price" />
                  </UFormGroup>
               </div>

               <!-- endregion: form -->

               <footer class="mt-10 flex justify-end">
                  <UButton
                     size="xl"
                     type="submit"
                     icon="i-uil:save"
                     :loading="isSubmitting"
                  >
                     Save
                  </UButton>
               </footer>

               <footer v-if="submitErrors">
                  <AlertError :description="submitErrors" />
               </footer>
            </UForm>
         </div>
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
