<script setup lang="ts">
import { z } from 'zod';
import type { Supplier } from '~~/_backend/suppliers/types';
import type { Category } from '~~/_backend/categories/types';
import { useApiFetchJewelry } from '~~/_backend/jewelries/api';
import SelectCategory from '~/components/categories/select-category.vue';
import SelectSupplier from '~/components/suppliers/select-supplier.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /jewelry/[id]/edit
 * Description: Edit jewelry
 */

definePageMeta({
   middleware: ['admin-only'],
});

useAppTitle('Edit jewelry');

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch jewelry details
 */
const { id } = useRoute().params;

const apiJewelry = reactive(useApiFetchJewelry({ id: Number(id) ?? 0 }));
apiJewelry.fetch();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Update jewelry details
 */

const selectedSupplier = ref<Supplier | null>(null);
const selectedCategory = ref<Category | null>(null);

const locations = [
   { id: 'DISPLAY', label: 'Display' },
   { id: 'STORE', label: 'Store' },
];

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z
   .object({
      stock_id: z.string(),
      purchased_date: z.string().date('Should be a valid date'),
      title: z.string().min(1, 'Title is required'),
      cost: z.coerce.number().positive('Needs to be a valid amount'),
      price: z.coerce.number().positive('Needs to be a valid amount'),
   })
   .refine(
      (data) => {
         return data.price >= data.cost;
      },
      {
         message: 'Price should be greater than the cost',
         path: ['price'],
      },
   );

const formState = reactive({
   id: 0,
   stock_id: '',
   purchased_date: '',
   category_id: 0,
   supplier_id: 0,
   title: '',
   description: '',
   karat: '22',
   weight: 0,
   cost: 0,
   price: 0,
   location: '',
});

const isSubmitting = ref(false);
const submitErrors = ref('');

/**
 * Handle submit form
 */
async function handleSubmit() {
   submitErrors.value = '';

   if (formState.category_id === 0) {
      submitErrors.value = 'Please choose a category';
      return;
   }
   if (formState.supplier_id === 0) {
      submitErrors.value = 'Please choose a supplier';
      return;
   }

   isSubmitting.value = true;

   try {
      const response = await useNuxtApp().$api<{ id: number }>('/jewelry/update', {
         method: 'PATCH',
         body: formState,
      });

      return navigateTo(`/jewelries/${response.id}`);
   }
   catch (error) {
      console.error(error);
      submitErrors.value = 'Failed to save the jewelry';
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */

watchDeep(
   () => apiJewelry.jewelry,
   (jewelry) => {
      if (!jewelry) {
         return;
      }

      formState.id = Number(jewelry.id);
      formState.stock_id = String(jewelry.stock_id);
      formState.purchased_date = jewelry.purchased_date;
      formState.category_id = Number(jewelry.category_id);
      formState.supplier_id = Number(jewelry.supplier_id);
      formState.title = jewelry.title;
      formState.description = jewelry.description;
      formState.karat = jewelry.karat;
      formState.weight = jewelry.weight;
      formState.cost = jewelry.cost;
      formState.price = jewelry.price;
      formState.location = jewelry.location;

      selectedSupplier.value = jewelry.supplier as Supplier;
      selectedCategory.value = jewelry.category as Category;
   },
   { immediate: true },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <div class="mx-auto max-w-3xl">
            <Heading1>Edit jewelry</Heading1>

            <UForm
               class="flex w-full flex-col gap-5"
               :schema="formSchema"
               :state="formState"
               @submit="handleSubmit"
            >
               <!-- region: form -->
               <div class="grid lg:grid-cols-3">
                  <UFormGroup
                     label="Purchased date"
                     name="purchased_date"
                  >
                     <InputDate v-model="formState.purchased_date" />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-2">
                  <UFormGroup label="Supplier">
                     <SelectSupplier v-model="selectedSupplier" />
                  </UFormGroup>

                  <UFormGroup label="Category">
                     <SelectCategory v-model="selectedCategory" />
                  </UFormGroup>
               </div>

               <div class="grid lg:grid-cols-3">
                  <UFormGroup
                     label="Stock ID"
                     description="Optional, If not provided, ID will be generated automatically."
                  >
                     <UInput v-model="formState.stock_id" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup
                     label="Title"
                     required
                     name="title"
                  >
                     <UInput v-model="formState.title" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup label="Description">
                     <UTextarea v-model="formState.description" />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-4">
                  <UFormGroup
                     label="Karat"
                     required
                  >
                     <SelectKarat v-model="formState.karat" />
                  </UFormGroup>

                  <UFormGroup
                     label="Weight"
                     required
                  >
                     <UInput v-model="formState.weight" />
                  </UFormGroup>
               </div>

               <div class="grid gap-5 lg:grid-cols-2">
                  <UFormGroup
                     label="Cost"
                     required
                     name="cost"
                  >
                     <UInput v-model="formState.cost" />
                  </UFormGroup>

                  <UFormGroup
                     label="Price"
                     required
                     name="price"
                  >
                     <UInput v-model="formState.price" />
                  </UFormGroup>
               </div>

               <div class="bg-orange-50 p-5 rounded-2xl grid gap-5">
                  <div class="grid lg:grid-cols-3 ">
                     <UFormGroup label="Location">
                        <USelectMenu
                           v-model="formState.location"
                           size="xl"
                           :options="locations"
                           value-attribute="id"
                           option-attribute="label"
                        />
                     </UFormGroup>
                  </div>

                  <div>
                     <p class="text-orange-500 font-bold text-sm">
                        Jewelries in store cannot be added to the bill
                     </p>
                  </div>
               </div>

               <!-- endregion: form -->

               <footer class="mt-10 flex justify-end">
                  <UButton
                     size="xl"
                     type="submit"
                     icon="i-uil:save"
                  >
                     Save
                  </UButton>
               </footer>

               <footer v-if="submitErrors">
                  <AlertError :description="submitErrors" />
               </footer>
            </UForm>
         </div>
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
