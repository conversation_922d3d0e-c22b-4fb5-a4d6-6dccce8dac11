<script setup lang="ts">
import { useApiFetchJewelry } from '~~/_backend/jewelries/api';
import { formatDateString, formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';
import { useApiFetchBill, useApiFetchBills } from '~~/_backend/bills/api';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /jewelry/[id]
 * Description: Single jewelry
 */

useAppTitle('Jewelry');

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;

const queryParams = reactive({
   id: id ? id.toString() : '',
});

const apiFetch = reactive(useApiFetchJewelry(queryParams));

apiFetch.fetch();

const billItem = computed(() => {
   if (apiFetch.jewelry && apiFetch.jewelry.billItem) {
      return apiFetch.jewelry.billItem;
   }

   return null;
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch the bill details if there is a bill
 */

const fetchBillQueryParams = reactive({
   id: 0,
});

const apiFetchBill = reactive(useApiFetchBill(fetchBillQueryParams));

watch(billItem, (bill) => {
   if (!bill) {
      return;
   }

   fetchBillQueryParams.id = Number(bill.bill_id);

   apiFetchBill.fetch();
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <LoadingIndicator v-if="apiFetch.status === 'pending'" />

         <!-- region: main -->
         <section v-if="apiFetch.jewelry">
            <!-- region: Edit jewelry -->
            <section
               v-if="user?.isAdmin && apiFetch.jewelry.sold === 'NO'"
               class="flex w-full justify-end"
            >
               <UButton
                  class="uppercase"
                  color="orange"
                  variant="soft"
                  icon="i-uil:pen"
                  :to="`/jewelries/${id}/edit`"
               >
                  Edit
               </UButton>
            </section>
            <!-- endregion: Edit jewelry -->

            <div class="flex w-full flex-col items-center justify-center">
               <Heading1>#{{ apiFetch.jewelry.id }}</Heading1>
               <Heading2>{{ apiFetch.jewelry.title }}</Heading2>

               <div class="my-5 flex w-full justify-center">
                  <StatsGroup>
                     <StatItem size="sm">
                        <template #title>
                           Supplier
                        </template>
                        <template #value>
                           {{ apiFetch.jewelry.supplier?.supplier_name }}
                        </template>
                     </StatItem>

                     <StatItem size="sm">
                        <template #title>
                           Category
                        </template>
                        <template #value>
                           {{ apiFetch.jewelry.category?.title }}
                        </template>
                        <template #footer>
                           hello
                        </template>
                     </StatItem>

                     <StatItem size="sm">
                        <template #title>
                           Purchased on
                        </template>
                        <template #value>
                           {{ apiFetch.jewelry.purchased_date }}
                        </template>
                     </StatItem>
                  </StatsGroup>
               </div>

               <!-- region: stats -->
               <div class="my-5 flex w-full justify-center">
                  <StatsGroup>
                     <StatItem>
                        <template #title>
                           Weight
                        </template>
                        <template #value>
                           {{ formatWeight(apiFetch.jewelry.weight) }}
                        </template>
                     </StatItem>

                     <StatItem>
                        <template #title>
                           Cost
                        </template>
                        <template #value>
                           {{ formatLKR(apiFetch.jewelry.cost) }}
                        </template>
                     </StatItem>

                     <StatItem>
                        <template #title>
                           Selling price
                        </template>
                        <template #value>
                           {{ formatLKR(apiFetch.jewelry.price) }}
                        </template>
                     </StatItem>
                  </StatsGroup>
               </div>
               <!-- endregion: stats -->

               <!-- region: status -->
               <div>
                  <template v-if="apiFetch.jewelry.sold === 'YES'">
                     <div class="item-status bg-red-200 text-red-500">
                        <UIcon name="i-uil:check-circle" />
                        <p>Sold</p>
                     </div>
                  </template>
                  <template v-else>
                     <template v-if="apiFetch.jewelry.location === 'DISPLAY'">
                        <div class="item-status bg-green-200 text-green-500">
                           <UIcon name="i-uil:check-circle" />
                           <p>In display</p>
                        </div>
                     </template>
                     <template v-if="apiFetch.jewelry.location === 'STORE'">
                        <div class="item-status bg-orange-200 text-orange-500">
                           <UIcon name="i-uil:check-circle" />
                           <p>In store</p>
                        </div>
                     </template>
                  </template>
               </div>
               <!-- endregion: status -->
            </div>

            <!-- ------------------------------------------------------------------------------- -->

            <!-- region: If there is a bill item -->
            <section
               v-if="billItem && apiFetchBill.bill"
               class="mt-10"
            >
               <UDivider class="mb-5">
                  Bill details
               </UDivider>

               <!-- region: Bill details -->
               <div class="flex flex-col items-center gap-5">
                  <div class="uppercase">
                     Billed to
                     <span class="font-bold">{{ apiFetchBill.bill.customer.customer_name }}</span>
                     on {{ formatDateString(apiFetchBill.bill.bill_date) }}.
                  </div>

                  <div class="text-xl font-bold">
                     Bill# {{ apiFetchBill.bill.id }}
                  </div>

                  <div>
                     <UButton
                        class="uppercase"
                        :to="`/bills/${billItem.bill_id}`"
                        variant="soft"
                     >
                        View bill
                     </UButton>
                  </div>
               </div>

               <!-- endregion: Bill details -->
            </section>
            <!-- endregion: If there is a bill item -->
         </section>
         <!-- endregion: main -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss">
.item-status {
   @apply flex items-center gap-3 rounded-2xl p-6 text-2xl font-bold uppercase;
}
</style>
