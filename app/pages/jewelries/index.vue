<script setup lang="ts">
import { useJewelryListingsStore } from '~/stores/jewelries-listings-store';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /jewelleries
 * Description: List all jewelleries
 */

useAppTitle('Jewelleries');

/* ---------------------------------------------------------------------------------------------- */

const jewelryListingsStore = useJewelryListingsStore();
const { queryParams } = storeToRefs(jewelryListingsStore);

onMounted(async () => {
   await jewelryListingsStore.fetch();
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: Toolbar -->
      <div class="mb-5">
         <JewelryListingsToolbar />
      </div>
      <!-- endregion: Toolbar -->

      <LoadingIndicator v-if="jewelryListingsStore.status === 'pending'" />

      <template v-else>
         <!-- region: summery -->
         <div class="mb-5 flex justify-center">
            <JewelryListingsSummery
               :items="jewelryListingsStore.paginatedData?.total"
               :cost="jewelryListingsStore.paginatedData?.total_cost"
               :price="jewelryListingsStore.paginatedData?.total_price"
               :weight="jewelryListingsStore.paginatedData?.total_weight"
            />
         </div>
         <!-- endregion: summery -->

         <!-- region: table -->
         <div class="mb-5">
            <div class="rounded-2xl border">
               <JewelryListingsTable :items="jewelryListingsStore.jewelries" />
            </div>
         </div>

         <div v-if="jewelryListingsStore.paginatedData">
            <p>There are {{ jewelryListingsStore.paginatedData.total }} items(s)</p>

            <PaginationContainer
               v-model="queryParams.page"
               :total="jewelryListingsStore.paginatedData.total"
            />
         </div>

         <!-- endregion: table -->
      </template>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
