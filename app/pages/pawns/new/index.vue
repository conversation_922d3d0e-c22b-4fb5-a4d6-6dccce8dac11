<script setup lang="ts">
import { z } from 'zod';
import dayjs from 'dayjs';
import type { Customer } from '~~/_backend/customers/types';
import { formatDateString } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */
/*
 * path: /pawns/new
 * description: create a new pawn
 */

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

const isAdmin = computed(() => {
   if (user) {
      return user.value.isAdmin;
   }
   return false;
});

/* ---------------------------------------------------------------------------------------------- */

interface CreatePawnBody {
   customer_id: string | number;
   pawn_item: string;
   karat: string;
   weight: number;
   value: number;
   amount: number;
   interest: number;
   pawn_date: string;
   pledge_date: string;
}

/* ---------------------------------------------------------------------------------------------- */

const selectedCustomer = ref<Customer | null>(null);
const selectedCustomerError = ref('');

const formSchema = z
   .object({
      customer_id: z.coerce.number().positive('Customer is not valid'),
      pawn_item: z.string().min(3, 'Pawning item name is required'),
      pawn_date: z.coerce.date(),
      pledge_date: z.coerce.date(),
      weight: z.coerce.number().positive('Should be a valid value'),
      value: z.coerce.number().positive('Should be a valid value'),
      amount: z.coerce.number().positive('Should be a valid value'),
   })
   .refine(
      (data) => {
         return data.pledge_date > data.pawn_date;
      },
      {
         message: 'Pledge date cannot be before pawning date',
         path: ['pledge_date'],
      },
   );

const formState = reactive({
   customer_id: null!,
   amount: 0,
   interest: 0,
   karat: '22',
   pawn_date: todayAsString(),
   pawn_item: '',
   pledge_date: toDateString(dayjs().add(1, 'y')),
   value: 0,
   weight: 0,
} as CreatePawnBody);

/**
 * Handle create pawn submit
 */
async function handleCreatePawn() {
   if (!selectedCustomer.value) {
      selectedCustomerError.value = 'Please choose a customer';
   }

   try {
      const response = await useNuxtApp().$api<{ id: string | number }>('/pawn/create', {
         method: 'POST',
         body: formState,
      });

      return navigateTo(`/pawns/${response.id}`);
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */

watch(selectedCustomer, (selectedCustomer) => {
   if (selectedCustomer) {
      formState.customer_id = selectedCustomer.id;
   }
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * Pledge dates group
 */

const pledgeDates = ref(['', '', '', '']);

watchImmediate(
   () => formState.pawn_date,
   (pawnDate) => {
      pledgeDates.value = [
         dayjs(pawnDate).add(1, 'month').format('YYYY-MM-DD'),
         dayjs(pawnDate).add(3, 'month').format('YYYY-MM-DD'),
         dayjs(pawnDate).add(6, 'month').format('YYYY-MM-DD'),
         dayjs(pawnDate).add(12, 'month').format('YYYY-MM-DD'),
      ];
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge class="mx-auto max-w-2xl">
         <!-- region: Header -->
         <Heading1>Create new pawn</Heading1>
         <!-- endregion: Header -->

         <UForm
            class="flex w-full flex-col gap-5"
            :schema="formSchema"
            :state="formState"
            @submit="handleCreatePawn"
         >
            <!-- region: Customer -->
            <div class="flex w-full flex-col gap-3 lg:flex-row">
               <UFormGroup
                  class="w-full"
                  name="customer_id"
               >
                  <SelectCustomer v-model="selectedCustomer" />
               </UFormGroup>
            </div>
            <!-- endregion: Customer -->

            <div class="grid grid-cols-1">
               <UFormGroup
                  label="Pawning item"
                  name="pawn_item"
               >
                  <UInput
                     v-model="formState.pawn_item"
                     placeholder="Eg. Pawning item name - Kappu, Small chain"
                  />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Pawning date">
                  <InputDate
                     v-model="formState.pawn_date"
                     :deny-past="!isAdmin"
                  />
               </UFormGroup>

               <UFormGroup
                  label="Pledge date"
                  name="pledge_date"
               >
                  <InputDate
                     v-model="formState.pledge_date"
                     allow-future
                     deny-past
                  />
               </UFormGroup>
            </div>

            <div>
               <p class="mb-1 text-xs uppercase">
                  Choose a pledge date
               </p>
               <div class="grid grid-cols-2 gap-3 lg:grid-cols-4">
                  <UButton
                     v-for="date in pledgeDates"
                     :key="date"
                     color="green"
                     variant="soft"
                     @click="formState.pledge_date = date"
                  >
                     {{ date }}
                  </UButton>
               </div>
            </div>

            <div class="grid grid-cols-2 gap-3 lg:grid-cols-4">
               <UFormGroup label="Karat (Kt)">
                  <UInput v-model="formState.karat" />
               </UFormGroup>

               <UFormGroup
                  label="Weight (g)"
                  name="weight"
               >
                  <UInput v-model="formState.weight" />
               </UFormGroup>

               <UFormGroup
                  label="Valued"
                  name="value"
               >
                  <UInput v-model="formState.value" />
               </UFormGroup>

               <UFormGroup
                  label="Amount paid"
                  name="amount"
               >
                  <UInput v-model="formState.amount" />
               </UFormGroup>
            </div>

            <footer class="mt-5 flex justify-end gap-3">
               <UButton type="submit">
                  Create pawn
               </UButton>
               <UButton
                  color="gray"
                  to="/pawns"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
