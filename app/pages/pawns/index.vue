<script setup lang="ts">
import PawnListingsTable from '~/page-components/pawns/listings/pawn-listings-table.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns
 * Description: List pawns
 */

useAppTitle('Pawns');

/* ---------------------------------------------------------------------------------------------- */

const pawnListingStore = usePawnListingsStore();
const { queryParams } = storeToRefs(pawnListingStore);

pawnListingStore.fetch();
</script>

<template>
   <AppLayout>
      <!-- region: toolbar -->
      <section class="mb-5">
         <PawnsListingToolbar />
      </section>
      <!-- endregion: toolbar -->

      <!-- region: Summery -->
      <section v-if="pawnListingStore.paginatedData" class="mb-5">
         <PawningListSummery
            :total-amount="pawnListingStore.paginatedData.total_amount"
            :total-value="pawnListingStore.paginatedData.total_value"
            :total-collected="pawnListingStore.paginatedData.total_collected"
            :total-count="pawnListingStore.paginatedData.total"
         />
      </section>
      <!-- endregion: Summery -->

      <!-- region: list -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <PawnListingsTable :items="pawnListingStore.pawns" />
         </div>
      </div>
      <!-- endregion: list -->

      <!-- region: footer -->
      <div v-if="pawnListingStore.paginatedData">
         <p>There are {{ pawnListingStore.paginatedData.total }} item(s)</p>

         <PaginationContainer
            v-model="queryParams.page"
            :total="pawnListingStore.paginatedData.total"
         />
      </div>
      <!-- endregion: footer -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
