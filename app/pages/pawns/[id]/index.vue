<script setup lang="ts">
import { useApiFetchPawn } from '~~/_backend/pawning/api';
import { formatDateString, formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';
import PawnInterestDetails from '~/page-components/pawns/single/pawn-interest-details.vue';
import PawnStateActions from '~/page-components/pawns/single/pawn-state-actions.vue';
import { usePawnStatus } from '~/composables/use-pawn-status';
import type { Pawn } from '~~/_backend/pawning/types';
import DeletePawn from '~/page-components/pawns/single/delete-pawn.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns/[id]
 * Description: Single pawn page
 */

useAppTitle('Pawn');

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;

const apiPawn = reactive(useApiFetchPawn(ref(Number(id))));
apiPawn.fetch();

/* ---------------------------------------------------------------------------------------------- */

const pawnRef = ref<Pawn>(null!);
const { pawnStatus } = usePawnStatus(pawnRef);

/* ---------------------------------------------------------------------------------------------- */

const stateColor = computed(() => {
   switch (pawnStatus.value) {
      case 'ACTIVE':
         return 'bg-green-500';
      case 'EXPIRED':
         return 'bg-red-500';
      case 'COLLECTED':
         return 'bg-gray-500';
      case 'UNMARKED':
         return 'bg-amber-500';
   }
   return '';
});

/* ---------------------------------------------------------------------------------------------- */

watch(
   () => apiPawn.pawn,
   (pawn) => {
      if (pawn)
         pawnRef.value = pawn;
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: Customer details -->
      <section
         v-if="apiPawn.pawn"
         class="mb-5 rounded-2xl bg-primaryColor-50 p-5"
      >
         <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
               <Heading2 class="uppercase">
                  {{ apiPawn.pawn.customer.customer_name }}
               </Heading2>
            </div>
            <div>
               <p class="text-sm uppercase text-secondaryColor-400">
                  Since
               </p>
               <p>{{ formatDateString(apiPawn.pawn.customer.since) }}</p>
            </div>
         </div>

         <div class="flex gap-5">
            <div
               v-if="apiPawn.pawn.customer.address"
               class="flex items-center gap-1"
            >
               <UIcon name="i-uil:location-pin-alt" />
               {{ apiPawn.pawn.customer.address }}
            </div>
            <div
               v-if="apiPawn.pawn.customer.phone"
               class="flex items-center gap-1"
            >
               <UIcon name="i-uil:phone" />
               {{ apiPawn.pawn.customer.phone }}
            </div>
            <div
               v-if="apiPawn.pawn.customer.nic"
               class="flex items-center gap-1"
            >
               <UIcon name="i-uil:file-landscape-alt" />
               {{ apiPawn.pawn.customer.nic }}
            </div>
         </div>
      </section>
      <!-- endregion: Customer details -->

      <!-- region: Actions -->

      <section class="flex justify-between">
         <div>
            <UButton
               v-if="apiPawn.pawn"
               :to="`/customers/${apiPawn.pawn.customer.id}`"
               icon="i-uil:user"
               color="gray"
               variant="soft"
            >
               View Customer
            </UButton>
         </div>

         <div
            v-if="apiPawn.pawn"
            class="py-5 flex gap-5 justify-end"
         >
            <PawnStateActions
               :pawn="apiPawn.pawn"
               @update="apiPawn.fetch()"
            />

            <div v-if="user && user.isAdmin" class="flex items-center gap-2">
               <UButton icon="i-uil:edit-alt" color="orange" :to="`/pawns/${id}/edit`">
                  Edit
               </UButton>
               <DeletePawn :pawn="apiPawn.pawn" @delete="() => navigateTo('/pawns')" />
            </div>
         </div>
      </section>
      <!-- endregion: Actions -->

      <!-- region: Pawn details -->
      <section v-if="apiPawn.pawn">
         <Heading1 class="flex items-center gap-2">
            <div
               class="size-8 rounded-full"
               :class="[stateColor]"
            ></div>
            # {{ apiPawn.pawn.id }} {{ apiPawn.pawn.pawn_item }}
         </Heading1>

         <!-- region: pawn basic -->
         <div class="mb-5 flex justify-center">
            <StatsGroup>
               <StatItem>
                  <template #title>
                     Weight
                  </template>

                  <template #value>
                     {{ formatWeight(apiPawn.pawn.weight) }}
                  </template>
               </StatItem>

               <StatItem>
                  <template #title>
                     Karat
                  </template>

                  <template #value>
                     {{ apiPawn.pawn.karat }}
                  </template>
               </StatItem>

               <StatItem>
                  <template #title>
                     Valued at
                  </template>

                  <template #value>
                     {{ formatLKR(apiPawn.pawn.value) }}
                  </template>
               </StatItem>

               <StatItem>
                  <template #title>
                     Amount paid
                  </template>

                  <template #value>
                     {{ formatLKR(apiPawn.pawn.amount) }}
                  </template>
               </StatItem>
            </StatsGroup>
         </div>

         <div class="flex justify-center">
            <StatsGroup>
               <StatItem size="sm">
                  <template #title>
                     Pawned date
                  </template>
                  <template #value>
                     {{ formatDateString(apiPawn.pawn.pawn_date) }}
                  </template>
               </StatItem>

               <StatItem size="sm">
                  <template #title>
                     Pledge date
                  </template>
                  <template #value>
                     {{ formatDateString(apiPawn.pawn.pledge_date) }}
                  </template>
               </StatItem>
            </StatsGroup>
         </div>

         <!-- region: Pawn state -->
         <section
            v-if="pawnStatus === 'EXPIRED'"
            class="my-5 flex w-full justify-center"
         >
            <div
               class="flex flex-col items-center rounded-2xl border border-red-100 bg-red-50 p-5 text-2xl font-bold text-red-500"
            >
               <p>Pawn is set as expired</p>
               <p>({{ apiPawn.pawn.expired_date }})</p>
            </div>
         </section>
         <!-- endregion: Pawn state -->

         <!-- region: Pawn interest dates -->
         <div
            v-if="apiPawn.pawn.state === 'ACTIVE'"
            class="py-5"
         >
            <PawnInterestDetails :pawn="apiPawn.pawn" />
         </div>

         <div
            v-if="apiPawn.pawn.state === 'COLLECTED'"
            class="mt-5 inline-flex flex-col items-center gap-5 justify-center text-center w-full"
         >
            <div
               class="rounded-2xl border text-center border-emerald-100 bg-emerald-50 p-5 text-2xl font-bold text-emerald-500"
            >
               <p class="mb-2">
                  Collected on {{ apiPawn.pawn.collected_date }}
               </p>
               <p class="text-4xl">
                  {{ formatLKR(apiPawn.pawn.collected_amount ?? 0) }}
               </p>
            </div>

            <!-- region: if admin, show set as active again -->
            <div v-if="user?.isAdmin">
               <PawnStateActionsEnablePawn
                  :pawn="apiPawn.pawn"
                  label="Set as active again"
                  @update="apiPawn.fetch()"
               />
            </div>
            <!-- endregion: if admin, show set as active again -->
         </div>
         <!-- endregion: Pawn interest dates -->

         <!-- endregion: pawn basic -->
      </section>
      <!-- endregion: Pawn details -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
