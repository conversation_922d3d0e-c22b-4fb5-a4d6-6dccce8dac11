<script setup lang="ts">
import { z } from 'zod';
import dayjs from 'dayjs';
import { useApiFetchPawn } from '~~/_backend/pawning/api';
import type { Pawn } from '~~/_backend/pawning/types';
import type { Customer } from '~~/_backend/customers/types';
import { formatDateString, toDateString, todayAsString } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns/[id]/edit
 * Description: Edit pawn page
 */

useAppTitle('Edit Pawn');

const { user } = useUserSession();
const toast = useToast();

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;

const apiPawn = reactive(useApiFetchPawn(ref(Number(id))));
apiPawn.fetch();

/* ---------------------------------------------------------------------------------------------- */

const selectedCustomer = ref<Customer | null>(null);
const selectedCustomerError = ref('');

const formSchema = z
   .object({
      pawn_item: z.string().min(3, 'Pawning item name is required'),
      pawn_date: z.coerce.date(),
      pledge_date: z.coerce.date(),
      weight: z.coerce.number().positive('Should be a valid value'),
      value: z.coerce.number().positive('Should be a valid value'),
      amount: z.coerce.number().positive('Should be a valid value'),
      discount: z.coerce.number().min(0, 'Should be a valid value'),
   })
   .refine(
      (data) => {
         return data.pledge_date > data.pawn_date;
      },
      {
         message: 'Pledge date cannot be before pawning date',
         path: ['pledge_date'],
      },
   );

const formState = reactive({
   id: Number(id),
   customer_id: 0,
   amount: 0,
   interest: 0,
   karat: '22',
   pawn_date: '',
   pawn_item: '',
   pledge_date: '',
   value: 0,
   weight: 0,
   discount: 0,
});

const isSubmitting = ref(false);
const submitErrors = ref('');

/**
 * Handle update pawn submit
 */
async function handleUpdatePawn() {
   isSubmitting.value = true;
   submitErrors.value = '';

   try {
      await useNuxtApp().$api('/pawn/update', {
         method: 'PATCH',
         body: formState,
      });

      toast.add({
         title: 'Success',
         description: 'Pawn updated successfully',
         color: 'green',
      });

      return navigateTo(`/pawns/${id}`);
   }
   catch (error) {
      console.error(error);
      submitErrors.value = 'Failed to update the pawn';
      toast.add({
         title: 'Error',
         description: 'Failed to update the pawn',
         color: 'red',
      });
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */

// Update form state when pawn data is loaded
watchEffect(() => {
   if (apiPawn.pawn) {
      // Keep the customer_id in the form state for the API call
      formState.customer_id = Number(apiPawn.pawn.customer_id);
      formState.amount = apiPawn.pawn.amount;
      formState.interest = 0; // Set interest to 0 as requested
      formState.karat = apiPawn.pawn.karat;
      formState.pawn_date = apiPawn.pawn.pawn_date;
      formState.pawn_item = apiPawn.pawn.pawn_item;
      formState.pledge_date = apiPawn.pawn.pledge_date;
      formState.value = apiPawn.pawn.value;
      formState.weight = apiPawn.pawn.weight;
      formState.discount = apiPawn.pawn.discount;

      // Store customer info for display purposes
      selectedCustomer.value = apiPawn.pawn.customer;
   }
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge class="mx-auto max-w-2xl">
         <header class="flex items-center justify-between">
            <Heading1>Edit Pawn</Heading1>
         </header>

         <UDivider class="mb-5" />

         <div v-if="!apiPawn.pawn" class="py-10 text-center">
            <UIcon name="i-uil:spinner" class="animate-spin text-4xl" />
            <p>Loading pawn details...</p>
         </div>

         <div v-else-if="apiPawn.pawn" class="mb-5 rounded-2xl bg-primaryColor-50 p-5">
            <div class="flex items-center justify-between">
               <div class="flex items-center gap-3">
                  <Heading2 class="uppercase">
                     {{ apiPawn.pawn.customer.customer_name }}
                  </Heading2>
               </div>
               <div>
                  <p class="text-sm uppercase text-secondaryColor-400">
                     Pawn ID
                  </p>
                  <p class="font-bold">
                     #{{ apiPawn.pawn.id }}
                  </p>
               </div>
            </div>

            <div class="flex gap-5">
               <div
                  v-if="apiPawn.pawn.customer.address"
                  class="flex items-center gap-1"
               >
                  <UIcon name="i-uil:location-pin-alt" />
                  {{ apiPawn.pawn.customer.address }}
               </div>
               <div
                  v-if="apiPawn.pawn.customer.phone"
                  class="flex items-center gap-1"
               >
                  <UIcon name="i-uil:phone" />
                  {{ apiPawn.pawn.customer.phone }}
               </div>
               <div
                  v-if="apiPawn.pawn.customer.nic"
                  class="flex items-center gap-1"
               >
                  <UIcon name="i-uil:file-landscape-alt" />
                  {{ apiPawn.pawn.customer.nic }}
               </div>
            </div>
         </div>

         <UForm
            v-if="apiPawn.pawn"
            class="flex w-full flex-col gap-5"
            :schema="formSchema"
            :state="formState"
            @submit="handleUpdatePawn"
         >
            <div class="grid grid-cols-1">
               <UFormGroup
                  label="Pawning item"
                  name="pawn_item"
               >
                  <UInput
                     v-model="formState.pawn_item"
                     placeholder="Eg. Pawning item name - Kappu, Small chain"
                  />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup
                  label="Pawn date"
                  name="pawn_date"
               >
                  <InputDate v-model="formState.pawn_date" />
               </UFormGroup>

               <UFormGroup
                  label="Pledge date"
                  name="pledge_date"
               >
                  <InputDate v-model="formState.pledge_date" allow-future />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3 lg:grid-cols-4">
               <UFormGroup label="Karat (Kt)">
                  <UInput v-model="formState.karat" />
               </UFormGroup>

               <UFormGroup
                  label="Weight (g)"
                  name="weight"
               >
                  <UInput v-model="formState.weight" />
               </UFormGroup>

               <UFormGroup
                  label="Valued"
                  name="value"
               >
                  <UInput v-model="formState.value" />
               </UFormGroup>

               <UFormGroup
                  label="Amount paid"
                  name="amount"
               >
                  <UInput v-model="formState.amount" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup
                  label="Discount"
                  name="discount"
               >
                  <UInput v-model="formState.discount" />
               </UFormGroup>
            </div>

            <p v-if="submitErrors" class="text-red-500">
               {{ submitErrors }}
            </p>

            <footer class="mt-5 flex justify-end gap-3">
               <UButton
                  type="submit"
                  :loading="isSubmitting"
                  :disabled="isSubmitting"
               >
                  Update pawn
               </UButton>
               <UButton
                  color="gray"
                  :to="`/pawns/${id}`"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
