<script setup lang="ts">
import PawnListingsTable from '~/page-components/pawns/listings/pawn-listings-table.vue';
import { todayAsString } from '~/utils/datetime-utils';

// We'll use a generic type for tab items to avoid import issues

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns/today
 * Description: Today's pawns page with tabs for created, collected, and expired pawns
 */

useAppTitle('Today\'s Pawns');

/* ---------------------------------------------------------------------------------------------- */

const pawnListingStore = usePawnListingsStore();
const { queryParams } = storeToRefs(pawnListingStore);

// Get today's date as string
const today = todayAsString();

// Define tab options
const route = useRoute();
const router = useRouter();

// Get tab from route query or default to 0
const activeTab = ref(Number(route.query.tab) || 0);

// Update URL when tab changes
watch(activeTab, (newTab) => {
   router.push({ query: { ...route.query, tab: String(newTab) } });
});

// Sort options
const sortByOptions = [
   { id: 'id', label: 'ID' },
   { id: 'customer_id', label: 'Customer' },
   { id: 'value', label: 'Value' },
   { id: 'weight', label: 'Weight' },
   { id: 'amount', label: 'Amount' },
   { id: 'pawn_date', label: 'Pawn Date' },
   { id: 'pledge_date', label: 'Pledge Date' },
   { id: 'state', label: 'State' },
];

const sortOrderOptions = [
   { id: 'asc', label: 'Ascending' },
   { id: 'desc', label: 'Descending' },
];

const tabItems = [
   {
      label: 'Created Today',
      icon: 'i-uil:plus-circle',
      value: 0,
   },
   {
      label: 'Collected Today',
      icon: 'i-uil:check-circle',
      value: 1,
   },
   {
      label: 'Expired Today',
      icon: 'i-uil:times-circle',
      value: 2,
   },
];

// Tab titles for display
const tabTitles: Record<number, string> = {
   0: 'Today\'s Created Pawns',
   1: 'Today\'s Collected Pawns',
   2: 'Today\'s Expired Pawns',
};

// Computed property to safely get the tab title
const currentTabTitle = computed(() => {
   // Convert activeTab.value to number to ensure it matches the tabTitles keys
   const tabIndex = Number(activeTab.value);
   return tabTitles[tabIndex] || 'Today\'s Pawns';
});

// Function to update query parameters based on selected tab
function updateQueryParams() {
   // Save current sort settings
   const currentSortBy = queryParams.value.sort_by;
   const currentSortOrder = queryParams.value.sort_order;

   // Reset all date filters first
   queryParams.value.pawn_date_min = '';
   queryParams.value.pawn_date_max = '';
   queryParams.value.collected_date_min = '';
   queryParams.value.collected_date_max = '';
   queryParams.value.pledge_date_min = '';
   queryParams.value.pledge_date_max = '';
   queryParams.value.state = null;

   // Set appropriate filters based on active tab
   switch (activeTab.value) {
      case 0: // Created Today
         // For created pawns, we only set pawn_date
         queryParams.value.pawn_date_min = today;
         queryParams.value.pawn_date_max = today;
         break;
      case 1: // Collected Today
         // For collected pawns, we only set collected_date
         queryParams.value.collected_date_min = today;
         queryParams.value.collected_date_max = today;
         break;
      case 2: // Expired Today
         // For expired pawns, we only set pledge_date
         queryParams.value.pledge_date_min = today;
         queryParams.value.pledge_date_max = today;
         break;
   }

   // Restore sort settings
   queryParams.value.sort_by = currentSortBy;
   queryParams.value.sort_order = currentSortOrder;

   // Reset page to 1 when changing tabs
   queryParams.value.page = 1;

   // Fetch pawns with updated filters
   pawnListingStore.fetch();
}

// Watch for tab changes to update query params
watch(activeTab, () => {
   updateQueryParams();
}, { immediate: true }); // This will run immediately on component mount

// Watch for sort changes to trigger a fetch
watch(
   () => [queryParams.value.sort_by, queryParams.value.sort_order],
   () => {
      pawnListingStore.fetch();
   },
);
</script>

<template>
   <AppLayout>
      <!-- region: header -->
      <section class="mb-5">
         <h1 class="text-2xl font-bold">
            {{ currentTabTitle }} ({{ today }})
         </h1>
      </section>
      <!-- endregion: header -->

      <!-- region: Tabs -->
      <section class="mb-5">
         <div class="flex gap-2 w-full bg-primaryColor-50 p-2 rounded-lg">
            <UButton
               v-for="tab in tabItems"
               :key="tab.value"
               :icon="tab.icon"
               :color="activeTab === tab.value ? 'purple' : 'gray'"
               :variant="activeTab === tab.value ? 'solid' : 'ghost'"
               class="flex-1"
               @click="activeTab = tab.value"
            >
               {{ tab.label }}
            </UButton>
         </div>
      </section>
      <!-- endregion: Tabs -->

      <!-- region: Sort options -->
      <section class="mb-5 flex flex-wrap gap-2 justify-end">
         <USelectMenu
            v-model="queryParams.sort_by"
            class="w-40"
            :options="sortByOptions"
            value-attribute="id"
            option-attribute="label"
            placeholder="Sort by"
         />
         <USelectMenu
            v-model="queryParams.sort_order"
            class="w-32"
            :options="sortOrderOptions"
            value-attribute="id"
            option-attribute="label"
            placeholder="Order"
         />
      </section>
      <!-- endregion: Sort options -->

      <!-- region: Summery -->
      <section v-if="pawnListingStore.paginatedData" class="mb-5">
         <PawningListSummery
            :total-amount="pawnListingStore.paginatedData.total_amount"
            :total-value="pawnListingStore.paginatedData.total_value"
            :total-collected="pawnListingStore.paginatedData.total_collected"
            :total-count="pawnListingStore.paginatedData.total"
         />
      </section>
      <!-- endregion: Summery -->

      <!-- region: list -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <PawnListingsTable :items="pawnListingStore.pawns" />
         </div>
      </div>
      <!-- endregion: list -->

      <!-- region: footer -->
      <div v-if="pawnListingStore.paginatedData">
         <p>There are {{ pawnListingStore.paginatedData.total }} item(s)</p>

         <PaginationContainer
            v-model="queryParams.page"
            :total="pawnListingStore.paginatedData.total"
         />
      </div>
      <!-- endregion: footer -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
