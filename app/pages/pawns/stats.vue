<script setup lang="ts">
import { useApiFetchPawningStats } from '~~/_backend/pawning/api';
import PawningStatsAll from '~/page-components/pawns/stats/pawning-stats-all.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns/stats
 * Description: Pawn stats
 */

definePageMeta({
   middleware: ['admin-only'],
});

useAppTitle('Pawning stats');

/* ---------------------------------------------------------------------------------------------- */

const minDatRef = ref(null);
const maxDateRef = ref(null);

const api = reactive(useApiFetchPawningStats(minDatRef, maxDateRef));

api.fetch();

const today = ref(todayAsString());

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <div class="flex flex-col gap-10">
         <PawningStatsAll
            v-model:date-min="today"
            v-model:date-max="today"
            title="Pawning stats - Today"
         />

         <PawningStatsAll title="Pawning stats - All" />

         <PawningStatsAll
            title="Pawning stats - Custom"
            custom-range
         />
      </div>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
