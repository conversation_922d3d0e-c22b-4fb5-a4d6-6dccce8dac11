<script setup lang="ts">
import PawnListingsTable from '~/page-components/pawns/listings/pawn-listings-table.vue';

/*
 * Path: /pawns/print
 * Description: Printable version of pawns list
 */

useAppTitle('Print Pawns List');

const pawnListingStore = usePawnListingsStore();
const { queryParams } = storeToRefs(pawnListingStore);

pawnListingStore.fetch();

// Auto-trigger print dialog when page loads
onMounted(() => {
   setTimeout(() => {
      window.print();
   }, 1000); // Small delay to ensure content is loaded
});

function handlePrint() {
   window.print();
}
</script>

<template>
   <div class="print:p-4 print:m-0 print-container">
      <!-- region: toolbar (hidden in print) -->
      <section class="mb-5 print:hidden flex flex-col gap-3">
         <PawnsListingToolbar :show-print-button="false" />

         <div class="flex justify-end">
            <UButton
               icon="heroicons:printer"
               @click="handlePrint"
            >
               Print Now
            </UButton>
         </div>
      </section>
      <!-- endregion: toolbar -->

      <!-- region: list -->
      <div class="print:shadow-none">
         <div class="rounded-2xl border print:border-none">
            <PawnListingsTable :items="pawnListingStore.pawns" />
         </div>
      </div>
      <!-- endregion: list -->

      <!-- region: pagination (hidden in print) -->
      <div v-if="pawnListingStore.paginatedData" class="mt-4 print:hidden">
         <PaginationContainer
            v-model="queryParams.page"
            :total="pawnListingStore.paginatedData.total"
         />
      </div>
      <!-- endregion: pagination -->
   </div>
</template>

<style scoped lang="postcss">
/* Print-specific styles */
@media print {
  /* Ensure the page uses A4 size */
  @page {
    size: A4 landscape; /* Changed to landscape for better table fit */
    margin: 0.5cm; /* Reduced margins */
  }

  html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  .print-container {
    width: 100%;
    padding: 0.5cm;
  }

  /* Reset visibility for all elements within print-container */
  .print-container,
  .print-container * {
    visibility: visible !important;
  }

  /* Hide all other elements */
  body > *:not(.print-container) {
    display: none;
  }

  /* Table specific adjustments */
  table {
    width: 100%;
    max-width: 100%;
    font-size: 11px; /* Smaller font size for better fit */
    border-collapse: collapse;
    page-break-inside: auto;
  }

  th, td {
    padding: 4px 8px;
  }

  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  thead {
    display: table-header-group;
  }

  tfoot {
    display: table-footer-group;
  }

  /* Force background colors and images to print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}
</style>
