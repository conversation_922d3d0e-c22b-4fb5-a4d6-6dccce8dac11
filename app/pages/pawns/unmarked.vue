<script setup lang="ts">
import PawnListingsTable from '~/page-components/pawns/listings/pawn-listings-table.vue';
import PaginationContainer from '~/components/container/pagination-container.vue';
import { useUnmarkedPawnsStore } from '~/stores/unmarked-pawns-store';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /pawns/unmarked
 * Description: Unmarked pawns page - shows active pawns with passed pledge dates
 */

useAppTitle('Unmarked Pawns');

/* ---------------------------------------------------------------------------------------------- */

const unmarkedPawnsStore = useUnmarkedPawnsStore();
const { queryParams } = storeToRefs(unmarkedPawnsStore);

// Fetch unmarked pawns using the dedicated API endpoint
unmarkedPawnsStore.fetch();

/* ---------------------------------------------------------------------------------------------- */

// Sort options
const sortByOptions = [
   { id: 'id', label: 'ID' },
   { id: 'customer_id', label: 'Customer' },
   { id: 'value', label: 'Value' },
   { id: 'weight', label: 'Weight' },
   { id: 'amount', label: 'Amount' },
   { id: 'pawn_date', label: 'Pawn Date' },
   { id: 'pledge_date', label: 'Pledge Date' },
   { id: 'state', label: 'State' },
];

const sortOrderOptions = [
   { id: 'asc', label: 'Ascending' },
   { id: 'desc', label: 'Descending' },
];

// Watch for sort by changes
watch(() => queryParams.value.sort_by, () => {
   unmarkedPawnsStore.fetch();
});

// Watch for sort order changes
watch(() => queryParams.value.sort_order, () => {
   unmarkedPawnsStore.fetch();
});

/* ---------------------------------------------------------------------------------------------- */

// Calculate summary values for unmarked pawns
const unmarkedSummary = computed(() => {
   return {
      total_amount: unmarkedPawnsStore.total_amount,
      total_value: unmarkedPawnsStore.total_value,
      total_collected: unmarkedPawnsStore.total_collected,
      total: unmarkedPawnsStore.paginatedData?.total || unmarkedPawnsStore.pawns.length || 0,
   };
});
</script>

<template>
   <AppLayout>
      <!-- region: header -->
      <section class="mb-5">
         <h1 class="text-2xl font-bold">
            Unmarked Pawns
         </h1>
         <p class="text-gray-500">
            Active pawns with passed pledge dates that need attention
         </p>
      </section>
      <!-- endregion: header -->

      <!-- region: Sort options -->
      <section class="mb-5 flex flex-wrap gap-2 justify-end">
         <USelectMenu
            v-model="queryParams.sort_by"
            class="w-40"
            :options="sortByOptions"
            value-attribute="id"
            option-attribute="label"
            placeholder="Sort by"
         />
         <USelectMenu
            v-model="queryParams.sort_order"
            class="w-32"
            :options="sortOrderOptions"
            value-attribute="id"
            option-attribute="label"
            placeholder="Order"
         />
      </section>
      <!-- endregion: Sort options -->

      <!-- region: Summery -->
      <section class="mb-5">
         <PawningListSummery
            :total-amount="unmarkedSummary.total_amount"
            :total-value="unmarkedSummary.total_value"
            :total-collected="unmarkedSummary.total_collected"
            :total-count="unmarkedSummary.total"
         />
      </section>
      <!-- endregion: Summery -->

      <!-- region: list -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <PawnListingsTable :items="unmarkedPawnsStore.pawns" />
         </div>
      </div>
      <!-- endregion: list -->

      <!-- region: footer -->
      <div v-if="unmarkedPawnsStore.pawns.length">
         <p>There are {{ unmarkedPawnsStore.paginatedData?.total || unmarkedPawnsStore.pawns.length }} unmarked pawn(s)</p>

         <PaginationContainer
            v-model="queryParams.page"
            :total="unmarkedPawnsStore.paginatedData?.total || 0"
         />
      </div>
      <div
         v-else
         class="py-10 text-center"
      >
         <p class="text-gray-500">
            No unmarked pawns found
         </p>
      </div>
      <!-- endregion: footer -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
