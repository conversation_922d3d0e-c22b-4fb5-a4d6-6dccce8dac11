<script setup lang="ts">
import { useApiFetchSupplier, useApiFetchSupplierJewelries } from '~~/_backend/suppliers/api';
import SingleSupplierInfo from '~/page-components/suppliers/single-supplier-info.vue';
import type { FetchSupplierJewelriesQueryParams } from '~~/_backend/suppliers/types';

/* ---------------------------------------------------------------------------------------------- */
/*
 * Path: /suppliers/[id]
 * Description: Single supplier
 */

const { id } = useRoute().params;
const supplierId = ref(Number(id));

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch supplier data
 */

const apiSupplier = reactive(useApiFetchSupplier(supplierId));

apiSupplier.fetch();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch supplier jewelries
 */

const queryParams = reactive({
   page: 1,
   supplier_id: supplierId.value,
} as FetchSupplierJewelriesQueryParams);

const apiJewelries = reactive(useApiFetchSupplierJewelries(queryParams));
apiJewelries.fetch();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: Supplier info -->
      <section
         v-if="apiSupplier.supplier"
         class="mb-10"
      >
         <SingleSupplierInfo
            :supplier="apiSupplier.supplier"
            @update="apiSupplier.fetch()"
         />
      </section>
      <!-- endregion: Supplier info -->

      <!-- region: Supplier jewelries -->
      <section>
         <header
            v-if="apiJewelries.paginatedData"
            class="mb-10"
         >
            <JewelryListingsSummery
               :items="apiJewelries.paginatedData.total"
               :cost="apiJewelries.paginatedData.total_cost"
               :price="apiJewelries.paginatedData.total_price"
               :weight="apiJewelries.paginatedData.total_weight"
            />
         </header>

         <LoadingIndicator v-if="apiJewelries.status === 'pending'" />
         <div v-else class="rounded-2xl border">
            <JewelryListingsTable :items="apiJewelries.jewelries" />
         </div>

         <footer v-if="apiJewelries.paginatedData" class="mt-5">
            <PaginationContainer
               v-model="queryParams.page"
               :page-count="apiJewelries.paginatedData.per_page"
               :total="apiJewelries.paginatedData.total"
            />
         </footer>
      </section>
      <!-- endregion: Supplier jewelries -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
