<script setup lang="ts">
import { useApiFetchSuppliers } from '~~/_backend/suppliers/api';
import SupplierListingsToolbar from '~/page-components/suppliers/supplier-listings-toolbar.vue';

/* ---------------------------------------------------------------------------------------------- */
/*
 * Path: /suppliers
 * Description: List all suppliers
 */

/* ---------------------------------------------------------------------------------------------- */

const queryParams = reactive({
   page: 1,
});

const apiFetchSuppliers = reactive(useApiFetchSuppliers(queryParams));
apiFetchSuppliers.fetch();

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   {
      key: 'supplier_name',
      label: 'Supplier Name',
   },
   {
      key: 'address',
      label: 'Address',
   },
   {
      key: 'phone',
      label: 'Phone',
   },
   {
      key: 'mobile',
      label: 'Mobile',
   },
];

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <Heading1>Suppliers</Heading1>

      <!-- region: toolbar -->
      <div class="mb-5">
         <SupplierListingsToolbar />
      </div>
      <!-- endregion: toolbar -->

      <section v-if="apiFetchSuppliers.paginatedSuppliers">
         <div class="mb-5 rounded-2xl border">
            <UTable
               :rows="apiFetchSuppliers.suppliers"
               :columns="columns"
            >
               <template #supplier_name-data="{ row }">
                  <UButton
                     class="uppercase"
                     variant="soft"
                     :to="`/suppliers/${row.id}`"
                  >
                     {{ row.supplier_name }}
                  </UButton>
               </template>
            </UTable>
         </div>

         <footer>
            <p>There are {{ apiFetchSuppliers.paginatedSuppliers.total }} suppliers</p>
         </footer>

         <footer>
            <PaginationContainer
               v-model="queryParams.page"
               :total="apiFetchSuppliers.paginatedSuppliers.total"
               :page-count="apiFetchSuppliers.paginatedSuppliers.per_page"
            />
         </footer>
      </section>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
