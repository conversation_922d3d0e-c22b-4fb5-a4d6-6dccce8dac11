<script setup lang="ts">
import DashboardStats from '~/page-components/dashboard/dashboard-stats.vue';
import { useAppTitle } from '~/composables/use-app-title';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /
 * Description: Dashboard
 */

useAppTitle('Dashboard');

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();
</script>

<template>
   <AppLayout>
      <!-- region: Welcome -->
      <section class="mb-10">
         <h1 class="text-4xl font-extrabold text-primaryColor-500">
            Welcome {{ user?.fullName }}
         </h1>
      </section>
      <!-- endregion: Welcome -->

      <DashboardStats v-if="user?.role === 'ADMIN'" />
   </AppLayout>
</template>

<style scoped lang="postcss">

</style>
