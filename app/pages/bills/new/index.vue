<script setup lang="ts">
import { z } from 'zod';
import type { CreateBillBody } from '~~/_backend/bills/types';
import type { Customer } from '~~/_backend/customers/types';
import CreateCustomer from '~/components/customers/create-customer.vue';
import type { FormErrorEvent } from '#ui/types';

/* ---------------------------------------------------------------------------------------------- */
/*
 * path: /bills/new
 * description: create a new bill
 */

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

const isAdmin = computed(() => {
   if (user) {
      return user.value.isAdmin;
   }
   return false;
});

/* ---------------------------------------------------------------------------------------------- */

const FormSchema = z.object({
   bill_date: z.string(),
   billed_by: z.string().min(1, 'Billed by is required'),
   description: z.string(),
});

const formState = reactive({
   customer_id: null!,
   bill_date: todayAsString(),
   billed_by: '',
   description: '',
} as CreateBillBody);

const selectedCustomer = ref<Customer | null>(null);

watch(selectedCustomer, (selectedCustomer) => {
   if (selectedCustomer) {
      formState.customer_id = selectedCustomer.id;
   }
});

const selectedCustomerError = ref('');

/**
 * Create a new bill
 */
async function handleCreateBill() {
   if (!selectedCustomer.value) {
      selectedCustomerError.value = 'Please choose a customer';
      return;
   }

   try {
      const response = await useNuxtApp().$api<{ id: string | number }>('/bill/create', {
         method: 'POST',
         body: formState,
      });

      console.log(response);

      return navigateTo(`/bills/${response.id}/edit`);
   }
   catch (e) {
      console.error(e);
   }
}

async function handleErrors(event: FormErrorEvent) {
   console.log(event.errors);
}

/* ---------------------------------------------------------------------------------------------- */

useAppTitle('Create a new bill');

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <!-- region: Billing Data -->
         <section class="mx-auto max-w-2xl">
            <!-- region: Header -->
            <Heading1>Create new bill</Heading1>
            <!-- endregion: Header -->

            <UForm
               class="flex w-full flex-col gap-5"
               :schema="FormSchema"
               :state="formState"
               @submit="handleCreateBill()"
               @error="handleErrors"
            >
               <!-- region: Customer -->
               <div class="flex w-full flex-col gap-3 lg:flex-row">
                  <SelectCustomer v-model="selectedCustomer" />
               </div>
               <!-- endregion: Customer -->

               <div class="flex gap-3">
                  <UFormGroup label="Bill date">
                     <InputDate
                        v-model="formState.bill_date"
                        :deny-past="!isAdmin"
                     />
                  </UFormGroup>

                  <UFormGroup
                     label="Billed by"
                     name="billed_by"
                     required
                  >
                     <UInput
                        v-model="formState.billed_by"
                        placeholder="Who is doing the bill?"
                     />
                  </UFormGroup>
               </div>

               <div>
                  <UFormGroup label="Description">
                     <UTextarea
                        v-model="formState.description"
                        placeholder="Optional description about the bill..."
                     ></UTextarea>
                  </UFormGroup>
               </div>

               <footer class="flex justify-end gap-3">
                  <UButton type="submit">
                     Create bill
                  </UButton>
                  <UButton
                     color="gray"
                     to="/bills"
                  >
                     Cancel
                  </UButton>
               </footer>

               <AlertError
                  v-if="selectedCustomerError"
                  :description="selectedCustomerError"
               />
            </UForm>
         </section>
         <!-- endregion: Billing Data -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
