<script setup lang="ts">
import BillsListingsToolbar from '~/page-components/bills/listings/bills-listings-toolbar.vue';
import { useBillListingsStore } from '~/stores/bill-listings-store';
import BillListingsTable from '~/page-components/bills/listings/bill-listings-table.vue';
import StatsGroup from '~/components/stats/stats-group.vue';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /bills
 * Description: List bill items
 */

useAppTitle('Bills');

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch bills
 */

const billListingsStore = useBillListingsStore();

const { queryParams } = storeToRefs(billListingsStore);

onMounted(async () => {
   await billListingsStore.fetch();
});

/* ---------------------------------------------------------------------------------------------- */

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: toolbar -->
      <div class="mb-5">
         <BillsListingsToolbar />
      </div>
      <!-- endregion: toolbar -->

      <!-- region: Bills Summary Header -->
      <div v-if="billListingsStore.billsSummary" class="mb-8">
         <div class="">
            <StatsGroup>
               <StatItem size="sm" color="blue">
                  <template #title>
                     Open Bills
                  </template>
                  <template #value>
                     {{ formatNumber(billListingsStore.billsSummary.open_count) }}
                  </template>
               </StatItem>

               <StatItem size="sm" color="green">
                  <template #title>
                     Closed Bills
                  </template>
                  <template #value>
                     {{ formatNumber(billListingsStore.billsSummary.closed_count) }}
                  </template>
               </StatItem>

               <StatItem size="sm" color="orange">
                  <template #title>
                     Total Amount
                  </template>
                  <template #value>
                     {{ formatLKR(billListingsStore.billsSummary.total_bill_amount) }}
                  </template>
               </StatItem>

               <StatItem size="sm" color="default">
                  <template #title>
                     Total Weight
                  </template>
                  <template #value>
                     {{ formatWeight(billListingsStore.billsSummary.total_weight) }}
                  </template>
               </StatItem>
            </StatsGroup>
         </div>
      </div>
      <!-- endregion: Bills Summary Header -->

      <!-- region: List -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <BillListingsTable :items="billListingsStore.bills" />
         </div>
      </div>

      <div v-if="billListingsStore.paginatedData">
         <p class="">
            There are {{ billListingsStore.paginatedData.total }} item(s)
         </p>

         <PaginationContainer v-model="queryParams.page" :total="billListingsStore.paginatedData.total" />
      </div>

      <!-- endregion: List -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
