<script setup lang="ts">
import BillsListingsToolbar from '~/page-components/bills/listings/bills-listings-toolbar.vue';
import { useBillListingsStore } from '~/stores/bill-listings-store';
import BillListingsTable from '~/page-components/bills/listings/bill-listings-table.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /bills
 * Description: List bill items
 */

useAppTitle('Bills');

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch bills
 */

const billListingsStore = useBillListingsStore();

const { queryParams } = storeToRefs(billListingsStore);

onMounted(async () => {
   await billListingsStore.fetch();
});

/* ---------------------------------------------------------------------------------------------- */

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <!-- region: toolbar -->
      <div class="mb-5">
         <BillsListingsToolbar />
      </div>
      <!-- endregion: toolbar -->

      <!-- region: summery -->
      <div class="my-5">
         <pre>{{ billListingsStore.billsSummary }}</pre>
      </div>
      <!-- endregion: summery -->

      <!-- region: List -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <BillListingsTable :items="billListingsStore.bills" />
         </div>
      </div>

      <div v-if="billListingsStore.paginatedData">
         <p class="">
            There are {{ billListingsStore.paginatedData.total }} item(s)
         </p>

         <PaginationContainer v-model="queryParams.page" :total="billListingsStore.paginatedData.total" />
      </div>

      <!-- endregion: List -->
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
