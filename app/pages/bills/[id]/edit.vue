<script setup lang="ts">
import dayjs from 'dayjs';
import type { FetchBillQueryParams, UpdateBillBody } from '~~/_backend/bills/types';
import { useApiFetchBill } from '~~/_backend/bills/api';
import BillItems from '~/page-components/bills/single/bill-items.vue';
import { formatLKR } from '~/utils/formatting-utils';
import AddBillDiscount from '~/page-components/bills/single/add-bill-discount.vue';
import api from '~/plugins/api';

/* ---------------------------------------------------------------------------------------------- */
/*
 * path: /bills/[id]/edit
 * description: edit bill details
 */

definePageMeta({
   middleware: [],
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch the bill details
 */

const { id } = useRoute().params;

const queryParams = reactive({
   id: id ? id.toString() : '',
} as FetchBillQueryParams);

const apiFetchBill = reactive(useApiFetchBill(queryParams));

apiFetchBill.fetch();

/* ---------------------------------------------------------------------------------------------- */

const discount = computed(() => {
   if (apiFetchBill.bill) {
      return formatLKR(apiFetchBill.bill.discount);
   }
   return formatLKR(0);
});

const tax = computed(() => {
   if (apiFetchBill.bill) {
      return formatLKR(apiFetchBill.bill.tax);
   }
   return formatLKR(0);
});

const subTotal = computed(() => {
   if (apiFetchBill.bill && apiFetchBill.bill.sub_total) {
      return formatLKR(apiFetchBill.bill.sub_total);
   }
   return formatLKR(0);
});

const billTotal = computed(() => {
   if (apiFetchBill.bill && apiFetchBill.bill.bill_total) {
      return formatLKR(apiFetchBill.bill.bill_total);
   }
   return formatLKR(0);
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * Update bill details
 */

const updateBillBody = reactive({
   id: 0,
   discount: 0,
   tax: 0,
   state: null!, // 'OPEN' or 'CLOSE'
} as UpdateBillBody);

/**
 * Open the bill
 */
async function switchBillStatus(status: 'OPEN' | 'CLOSE') {
   try {
      updateBillBody.state = status;

      await useNuxtApp().$api('/bill/update', {
         method: 'PATCH',
         body: updateBillBody,
      });

      await apiFetchBill.fetch();
   }
   catch (e) {
      console.error(e);
   }
}

/**
 * Update bill details such as discount, tax
 */

/* ---------------------------------------------------------------------------------------------- */

watch(
   () => apiFetchBill.bill,
   (bill) => {
      if (!bill)
         return;

      /**
       * set title
       */
      useAppTitle(`Edit bill (${bill.id})`);

      /**
       * update data
       */
      updateBillBody.id = bill.id;
      updateBillBody.discount = bill.discount;
      updateBillBody.tax = bill.tax;
      updateBillBody.state = bill.state;
   },
);

/* ---------------------------------------------------------------------------------------------- */

function backToView() {
   const { id } = useRoute().params;

   if (id) {
      return navigateTo(`/bills/${id}`);
   }
}

/* ---------------------------------------------------------------------------------------------- */
/*
 * Limit normal user access if the billing date is in the past
 */

const { user } = useUserSession();

const hasAccess = computed(() => {
   if (user.value && !user.value.isAdmin) {
      const billDate = dayjs(apiFetchBill.bill?.bill_date);
      const today = dayjs();

      return today.diff(billDate, 'day') === 0;
   }

   return true;
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <template v-if="hasAccess">
            <!-- region: Bill details -->
            <section v-if="apiFetchBill.bill">
               <!-- region: bill header -->
               <BillHeader :bill="apiFetchBill.bill" />
               <!-- endregion: bill header -->

               <!-- region: add bill item -->
               <div
                  v-if="apiFetchBill.bill.state === 'OPEN'"
                  class="mb-5 flex justify-end gap-3"
               >
                  <SearchAddJewelries
                     :bill="apiFetchBill.bill"
                     @update="apiFetchBill.fetch()"
                  />

                  <AddBillDiscount
                     :bill="apiFetchBill.bill"
                     @update="apiFetchBill.fetch()"
                  />
               </div>
               <!-- endregion: add bill item -->

               <!-- region: Bill items -->
               <section class="rounded-2xl border">
                  <BillItems
                     :items="apiFetchBill.bill.bill_items"
                     :bill-state="apiFetchBill.bill.state"
                     @delete="apiFetchBill.fetch()"
                  />
               </section>
               <!-- endregion: Bill items -->

               <!-- region: Bill footer -->
               <footer class="mt-10 justify-end flex">
                  <StatsGroup>
                     <StatItem size="sm">
                        <template #title>
                           Discount / Old gold
                        </template>

                        <template #value>
                           {{ discount }}
                        </template>
                     </StatItem>

                     <StatItem size="sm">
                        <template #title>
                           Tax
                        </template>

                        <template #value>
                           {{ tax }}
                        </template>
                     </StatItem>

                     <StatItem size="sm">
                        <template #title>
                           Subtotal
                        </template>

                        <template #value>
                           {{ subTotal }}
                        </template>
                     </StatItem>

                     <StatItem size="sm">
                        <template #title>
                           Bill total
                        </template>

                        <template #value>
                           {{ billTotal }}
                        </template>
                     </StatItem>
                  </StatsGroup>
               </footer>
               <!-- endregion: Bill footer -->
            </section>
            <!-- endregion: Bill details -->

            <template v-if="apiFetchBill.bill">
               <UDivider
                  class="my-10"
                  size="xl"
               >
                  <span class="text-sm uppercase text-secondaryColor-500">Management options</span>
               </UDivider>

               <section class="flex justify-center gap-5">
                  <UButton
                     v-if="apiFetchBill.bill?.state === 'CLOSE'"
                     class="uppercase"
                     color="red"
                     icon="i-uil:book-open"
                     @click="switchBillStatus('OPEN')"
                  >
                     Open the bill
                  </UButton>
                  <UButton
                     v-else
                     class="uppercase"
                     color="green"
                     icon="i-uil:check"
                     @click="switchBillStatus('CLOSE')"
                  >
                     Close the bill
                  </UButton>

                  <UButton
                     color="gray"
                     icon="i-uil:backspace"
                     @click="backToView()"
                  >
                     Back to view
                  </UButton>
               </section>
            </template>
         </template>

         <!-- region: user has no access -->
         <template v-else>
            <p class="text-center">
               You have no access to edit this bill
            </p>
         </template>
         <!-- endregion: user has no access -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
