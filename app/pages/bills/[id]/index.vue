<script setup lang="ts">
import { useApiFetchBill } from '~~/_backend/bills/api';
import type { FetchBillQueryParams, UpdateBillBody } from '~~/_backend/bills/types';
import BillItems from '~/page-components/bills/single/bill-items.vue';
import { formatLKR } from '~/utils/formatting-utils';
import IsAdminUser from '~/components/is-admin-user.vue';

/* ---------------------------------------------------------------------------------------------- */

/*
 * path: /bills/[id]
 * description: display bill details
 */

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;

const queryParams = reactive({
   id: id ? id.toString() : '',
} as FetchBillQueryParams);

const apiFetchBill = reactive(useApiFetchBill(queryParams));

apiFetchBill.fetch();

/* ---------------------------------------------------------------------------------------------- */

const openBill = computed(() => {
   if (apiFetchBill.bill) {
      return apiFetchBill.bill.state === 'OPEN';
   }
   return false;
});

const discount = computed(() => {
   if (apiFetchBill.bill) {
      return formatLKR(apiFetchBill.bill.discount);
   }
   return formatLKR(0);
});

const tax = computed(() => {
   if (apiFetchBill.bill) {
      return formatLKR(apiFetchBill.bill.tax);
   }
   return formatLKR(0);
});

const subTotal = computed(() => {
   if (apiFetchBill.bill && apiFetchBill.bill.sub_total) {
      return formatLKR(apiFetchBill.bill.sub_total);
   }
   return formatLKR(0);
});

const billTotal = computed(() => {
   if (apiFetchBill.bill && apiFetchBill.bill.bill_total) {
      return formatLKR(apiFetchBill.bill.bill_total);
   }
   return formatLKR(0);
});

/* ---------------------------------------------------------------------------------------------- */
/*
 * close the bill
 */

const updateBillBody = reactive({
   id: 0,
   discount: 0,
   tax: 0,
   state: null!, // 'OPEN' or 'CLOSE'
} as UpdateBillBody);

/**
 * Open the bill
 */
async function handleCloseBill() {
   try {
      updateBillBody.state = 'CLOSE';

      await useNuxtApp().$api('/bill/update', {
         method: 'PATCH',
         body: updateBillBody,
      });

      await apiFetchBill.fetch();
   }
   catch (e) {
      console.error(e);
   }
}

/* ---------------------------------------------------------------------------------------------- */

watch(
   () => apiFetchBill.bill,
   (bill) => {
      if (!bill)
         return;

      /**
       * set title
       */
      useAppTitle(`Viewing bill (${bill.id})`);

      /**
       * update data
       */
      updateBillBody.id = bill.id;
      updateBillBody.discount = bill.discount;
      updateBillBody.tax = bill.tax;
      updateBillBody.state = bill.state;
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge>
         <LoadingIndicator v-if="apiFetchBill.status === 'pending'" />

         <!-- region: Header options -->
         <div class="flex w-full justify-end">
            <UButton
               v-if="!openBill"
               icon="i-uil:print"
               :to="`/bills/${id}/print`"
            >
               PRINT BILL
            </UButton>
         </div>

         <div class="h-5"></div>

         <!-- endregion: Header options -->

         <!-- region: Bill state notice -->
         <div class="flex w-full justify-center">
            <div
               v-if="apiFetchBill.bill?.state === 'OPEN'"
               class="mb-5 flex flex-col items-center rounded-xl bg-red-50 p-5 text-red-500"
            >
               <h2 class="text-xl font-bold">
                  This bill is still open!
               </h2>
               <p class="text-red-400">
                  Open bills can be modified. Only closed bill can be printed.
               </p>

               <UButton
                  color="green"
                  @click="handleCloseBill()"
               >
                  Close this bill
               </UButton>
            </div>
         </div>
         <!-- endregion: Bill state notice -->

         <section v-if="apiFetchBill.bill">
            <!-- region: bill header -->
            <BillHeader :bill="apiFetchBill.bill" />
            <!-- endregion: bill header -->

            <!-- region: Bill items -->
            <section class="rounded-2xl border">
               <BillItems
                  :items="apiFetchBill.bill.bill_items"
                  :bill-state="apiFetchBill.bill.state"
                  readonly
               />
            </section>
            <!-- endregion: Bill items -->

            <!-- region: Bill footer -->
            <footer class="mt-10 justify-end flex">
               <StatsGroup>
                  <StatItem size="sm">
                     <template #title>
                        Discount / Old gold
                     </template>

                     <template #value>
                        {{ discount }}
                     </template>
                  </StatItem>

                  <StatItem size="sm">
                     <template #title>
                        Tax
                     </template>

                     <template #value>
                        {{ tax }}
                     </template>
                  </StatItem>

                  <StatItem size="sm">
                     <template #title>
                        Subtotal
                     </template>

                     <template #value>
                        {{ subTotal }}
                     </template>
                  </StatItem>

                  <StatItem size="sm">
                     <template #title>
                        Bill total
                     </template>

                     <template #value>
                        {{ billTotal }}
                     </template>
                  </StatItem>
               </StatsGroup>
            </footer>
            <!-- endregion: Bill footer -->
         </section>

         <!-- region: Admin options -->

         <UDivider size="xl" class="my-10">
            <span class="uppercase text-sm text-secondaryColor-500">Management options</span>
         </UDivider>

         <section class="flex justify-center gap-5">
            <!--            <IsAdminUser> -->
            <!--               <template #true> -->
            <!--                  <UButton -->
            <!--                     icon="i-uil:edit-alt" -->
            <!--                     color="orange" -->
            <!--                     :to="`/bills/${id}/edit`" -->
            <!--                     class="uppercase" -->
            <!--                  > -->
            <!--                     Edit bill -->
            <!--                  </UButton> -->
            <!--               </template> -->

            <!--               <template #false> -->
            <!--                  Only admin user can access these features. -->
            <!--               </template> -->
            <!--            </IsAdminUser> -->

            <UButton
               icon="i-uil:edit-alt"
               color="orange"
               :to="`/bills/${id}/edit`"
               class="uppercase"
            >
               Edit bill
            </UButton>
         </section>

         <!-- endregion: Admin options -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
