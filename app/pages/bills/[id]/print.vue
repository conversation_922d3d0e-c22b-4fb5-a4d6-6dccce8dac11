<script setup lang="ts">
import type { FetchBillQueryParams } from '~~/_backend/bills/types';
import { useApiFetchBill } from '~~/_backend/bills/api';

/* ---------------------------------------------------------------------------------------------- */
/*
 * path: /bills/[id]/print
 * description: print bill details
 */

/* ---------------------------------------------------------------------------------------------- */
/*
 * Fetch the bill details
 */

const { id } = useRoute().params;

const queryParams = reactive({
   id: id ? id.toString() : '',
} as FetchBillQueryParams);

const apiFetchBill = reactive(useApiFetchBill(queryParams));

apiFetchBill.fetch();

/* ---------------------------------------------------------------------------------------------- */

function openPrintDialog() {
   window.print();
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <section class="mx-auto flex w-full flex-col items-center justify-center p-10 print:p-0">
      <div v-if="apiFetchBill.bill && apiFetchBill.bill.state === 'CLOSE'">
         <div class="h-[5.83in] w-[8.27in] border px-[0.5in] py-[0.5in]">
            <header class="mb-[0.5in] flex w-full justify-center">
               <h1 class="text-xl font-bold uppercase">
                  ABIRA JEWELLERS
               </h1>
            </header>

            <header class="flex justify-between">
               <aside>
                  <h2 class="font-bold">
                     {{ apiFetchBill.bill.customer.customer_name }}
                  </h2>
                  <p>Tel: {{ apiFetchBill.bill.customer.phone }}</p>
               </aside>
               <aside>
                  <h1 class="font-bold">
                     Bill #{{ apiFetchBill.bill.id }}
                  </h1>
                  <p>{{ formatDateString(apiFetchBill.bill.bill_date) }}</p>
               </aside>
            </header>
            <hr />

            <div class="h-[0.3in]"></div>

            <section class="py-[0.2in]">
               <table class="mb-[0.2in] w-full">
                  <thead>
                     <tr>
                        <th class="text-right">
                           Item #
                        </th>
                        <th class="text-right">
                           Item
                        </th>
                        <th class="text-right">
                           Kt
                        </th>
                        <th class="text-right">
                           Weight
                        </th>
                        <th class="text-right">
                           Amount
                        </th>
                     </tr>
                  </thead>

                  <tbody>
                     <tr
                        v-for="item in apiFetchBill.bill.bill_items"
                        :key="item.id"
                     >
                        <td class="text-right">
                           {{ item.id }}
                        </td>
                        <td class="text-right">
                           {{ item.jewelry.title }}
                        </td>
                        <td class="text-right">
                           {{ item.jewelry.karat }}
                        </td>
                        <td class="text-right">
                           {{ formatWeight(item.jewelry.weight) }}
                        </td>
                        <td class="text-right">
                           {{ formatLKR(item.jewelry.price) }}
                        </td>
                     </tr>
                  </tbody>
               </table>

               <hr />

               <div class="mt-[0.2in] flex justify-end gap-5 font-bold">
                  <div>Weight: {{ formatWeight(apiFetchBill.bill.bill_items_weight) }}</div>
                  <div>Discount / Old gold: {{ formatLKR(apiFetchBill.bill.discount) }}</div>
                  <div>Total amount: {{ formatLKR(apiFetchBill.bill.bill_items_total - apiFetchBill.bill.discount) }}</div>
               </div>
            </section>

            <footer></footer>
         </div>

         <!-- region: print actions -->
         <div class="mt-10 flex flex-col items-center justify-center gap-3 print:hidden">
            <UButton
               icon="i-uil:print"
               @click="openPrintDialog()"
            >
               Open Print Dialog
            </UButton>
            <UButton
               color="gray"
               :to="`/bills/${id}`"
               icon="i-uil:backspace"
            >
               Back
            </UButton>
         </div>
         <!-- endregion: print actions -->
      </div>

      <div v-else>
         <p class="text-center text-2xl font-bold">
            Cannot print open bills.
         </p>
      </div>
   </section>
</template>

<style scoped lang="postcss"></style>
