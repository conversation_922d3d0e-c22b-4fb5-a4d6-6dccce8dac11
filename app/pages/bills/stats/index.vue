<script setup lang="ts">
/* ---------------------------------------------------------------------------------------------- */

/*
 * Path: /bills/stats
 * Description: Display bill stats
 */

import AllBillsStats from '~/page-components/bills/stats/all-bills-stats.vue';
import TodayBillsStats from '~/page-components/bills/stats/today-bills-stats.vue';
import CustomBillsStats from '~/page-components/bills/stats/custom-bills-stats.vue';

useAppTitle('Bills stats');

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <section class="flex flex-col gap-10">
         <AllBillsStats />

         <UDivider />

         <TodayBillsStats />

         <UDivider />

         <CustomBillsStats />
      </section>
   </applayout>
</template>

<style scoped lang="postcss">

</style>
