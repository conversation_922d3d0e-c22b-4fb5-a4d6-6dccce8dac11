<script setup lang="ts">
import { type ApiFetchCustomersQueryParams, useApiFetchCustomers } from '~~/_backend/customers/api';
import CustomerListingsToolbar
   from '~/page-components/customers/listings/customer-listings-toolbar.vue';
import CustomerListingsTable
   from '~/page-components/customers/listings/customer-listings-table.vue';

/* ---------------------------------------------------------------------------------------------- */

useAppTitle('Customers');

/* ---------------------------------------------------------------------------------------------- */

const queryParams = reactive({
   page: 1,
   query: '',
   sort_by: 'since',
   sort_order: 'asc',

} as ApiFetchCustomersQueryParams);

const apiCustomers = reactive(useApiFetchCustomers(queryParams));
apiCustomers.fetch();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Path: /customers
 * Description: List all customers
 */
/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <Heading1>Customers</Heading1>

      <!-- region: toolbar -->
      <div class="mb-5">
         <CustomerListingsToolbar v-model="queryParams" />
      </div>
      <!-- endregion: toolbar -->

      <!-- region: list -->
      <div class="mb-5">
         <div class="rounded-2xl border">
            <CustomerListingsTable :items="apiCustomers.customers" />
         </div>
      </div>

      <!-- region: pagination -->
      <div v-if="apiCustomers.paginatedData">
         <p>There are {{ apiCustomers.paginatedData.total }} items(s)</p>
         <PaginationContainer
            v-model="queryParams.page"
            :total="apiCustomers.paginatedData.total"
         />
      </div>
      <!-- endregion: pagination -->

      <!-- endregion: list -->
   </AppLayout>
</template>

<style scoped lang="postcss">

</style>
