<script setup lang="ts">
import { useApiFetchCustomer } from '~~/_backend/customers/api';
import CustomerInfo from '~/page-components/customers/single/customer-info.vue';
import SingleCustomerPawns from '~/page-components/customers/single/single-customer-pawns.vue';

useAppTitle('Customer');

/* ---------------------------------------------------------------------------------------------- */

const { id } = useRoute().params;

const apiCustomer = reactive(useApiFetchCustomer(ref(Number(id))));

apiCustomer.fetch();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Path: /customer/[id]
 * Description: Single customer
 */
/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AppLayout>
      <ContainerLarge v-if="apiCustomer.customer">
         <CustomerInfo :customer="apiCustomer.customer" />

         <div class="h-10" />

         <!-- region: pawning -->
         <div v-if="apiCustomer.customer.has_pawns">
            <Heading1>Pawning details</Heading1>

            <SingleCustomerPawns :customer="apiCustomer.customer" />
         </div>
         <!-- endregion: pawning -->

         <div class="h-10" />

         <!-- region: bills -->
         <div v-if="apiCustomer.customer.has_bills">
            <Heading1>Bill details</Heading1>

            <SingleCustomerBills :customer="apiCustomer.customer" />
         </div>
         <!-- endregion: bills -->
      </ContainerLarge>
   </AppLayout>
</template>

<style scoped lang="postcss"></style>
