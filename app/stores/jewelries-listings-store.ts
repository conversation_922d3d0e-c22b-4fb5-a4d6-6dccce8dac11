import type { FetchJewelriesQueryParams } from '~~/_backend/jewelries/api';
import { useApiFetchJewelries } from '~~/_backend/jewelries/api';

/**
 * Bill listings
 */
export const useJewelryListingsStore = defineStore('jewelry-listings', () => {
   const queryParams = ref({
      page: 1,
      query: '',
      karat: 'ALL',
      location: 'ALL',
      sold: 'ALL',
      sort_by: 'id',
      sort_order: 'desc',
      stock_id: '',
      cost_max: 0,
      cost_min: 0,
      price_max: 0,
      price_min: 0,
      weight_max: 0,
      weight_min: 0,
   } as FetchJewelriesQueryParams);

   const { fetch, error, status, paginatedData, jewelries } = useApiFetchJewelries(queryParams);

   return {
      queryParams,
      fetch,
      error,
      status,
      paginatedData,
      jewelries,
   };
});
