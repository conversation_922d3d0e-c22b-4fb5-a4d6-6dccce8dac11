import { todayAsString } from '~/utils/datetime-utils';
import type { FetchBillsQueryParams } from '~~/_backend/bills/types';
import { useApiFetchBills } from '~~/_backend/bills/api';

/**
 * Bill listings
 */
export const useBillListingsStore = defineStore('bill-listings', () => {
   const queryParams = reactive({
      bill_id: 0,
      date_max: todayAsString(),
      date_min: todayAsString(),
      page: 1,
      sort_by: 'id',
      sort_order: 'desc',
      state: null,
   } as FetchBillsQueryParams);

   const { fetch, bills, error, status, paginatedData, billsSummary } = useApiFetchBills(queryParams);

   return {
      queryParams,
      fetch,
      bills,
      error,
      status,
      paginatedData,
      billsSummary,
   };
});
