import type { FetchUnmarkedPawnsQueryParams } from '~~/_backend/pawning/api';
import { useApiFetchUnmarkedPawns } from '~~/_backend/pawning/api';

/**
 * Unmarked pawns store
 */
export const useUnmarkedPawnsStore = defineStore('unmarked-pawns', () => {
   // Define the query params with explicit type to ensure TypeScript recognizes all properties
   const queryParams = reactive<FetchUnmarkedPawnsQueryParams>({
      page: 1,
      pledge_date_min: '',
      sort_by: 'pledge_date',
      sort_order: 'desc',
   });

   const { fetch, pawns, paginatedData, error, status } = useApiFetchUnmarkedPawns(queryParams);

   // Calculate totals for the summary
   const total_amount = computed(() => {
      if (!pawns.value.length)
         return 0;
      return pawns.value.reduce((sum, pawn) => sum + pawn.amount, 0);
   });

   const total_value = computed(() => {
      if (!pawns.value.length)
         return 0;
      return pawns.value.reduce((sum, pawn) => sum + pawn.value, 0);
   });

   const total_collected = computed(() => 0); // Unmarked pawns are not collected

   return {
      queryParams,
      fetch,
      paginatedData,
      error,
      status,
      pawns,
      total_amount,
      total_value,
      total_collected,
   };
});
