import type { FetchPawningQueryParams } from '~~/_backend/pawning/api';
import { useApiFetchPawns } from '~~/_backend/pawning/api';

export const usePawnListingsStore = defineStore('pawn-listings', () => {
   const queryParams = reactive({
      page: 1,
      pawn_id: 0,
      collected_date_max: '',
      collected_date_min: '',
      pawn_date_max: '',
      pawn_date_min: '',
      pledge_date_max: '',
      pledge_date_min: '',
      weight_min: undefined,
      weight_max: undefined,
      sort_by: 'pawn_date',
      sort_order: 'desc',
      state: null,
   } as FetchPawningQueryParams);

   const { fetch, pawns, paginatedData, error, status } = useApiFetchPawns(queryParams);

   return {
      queryParams,
      fetch,
      paginatedData,
      error,
      status,
      pawns,
   };
});
