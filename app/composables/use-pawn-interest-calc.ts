import dayjs from 'dayjs';
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

export function useEnvInterestRates() {
   const {
      interestRateFirstWeek,
      interestRateSecondWeek,
      interestRateThirdWeek,
      interestRateFourthWeek,
   } = useRuntimeConfig().public;

   return {
      firstWeek: Number(interestRateFirstWeek ?? 0),
      secondWeek: Number(interestRateSecondWeek ?? 0),
      thirdWeek: Number(interestRateThirdWeek ?? 0),
      fourthWeek: Number(interestRateFourthWeek ?? 0),
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Calculate pawn interest
 */
export function usePawnInterestCalc() {
   const pawn = ref<Pawn | null>(null);
   const today = dayjs();

   function setPawn(pawnVal: Pawn) {
      pawn.value = pawnVal;
   }

   const pawnedDays = computed(() => {
      if (!pawn.value)
         return;
      return dayjs(pawn.value.pledge_date).diff(dayjs(pawn.value.pawn_date), 'days');
   });

   const daysSincePawned = computed(() => {
      if (!pawn.value)
         return;

      return today.diff(pawn.value.pawn_date, 'days');
   });

   /* ------------------------------------------------------------------------------------------- */

   interface InterestCalculationResult {
      totalInterestAmount: number;
      monthlyBreakdown: Array<{
         month: number;
         days: number;
         interestRate: number;
         interestAmount: number;
      }>;
   }

   function calculateCompoundInterest(amount: number, totalDays: number): InterestCalculationResult {
      const envRates = useEnvInterestRates();

      // Function to get precise interest rate
      const getInterestRate = (daysInPeriod: number): number => {
         // If days are less than 7, use 0.5% rate
         if (daysInPeriod <= 7)
            return envRates.firstWeek;

         // If days are between 8-14, use 1% rate
         if (daysInPeriod <= 14)
            return envRates.secondWeek;

         // If days are between 15-21, use 1.5% rate
         if (daysInPeriod <= 21)
            return envRates.thirdWeek;

         // If days are 22-30, use 1.9% rate
         return envRates.fourthWeek;
      };

      // Calculate monthly breakdown
      const monthlyBreakdown: Array<{
         month: number;
         days: number;
         interestRate: number;
         interestAmount: number;
      }> = [];

      let remainingDays = totalDays;
      let currentMonth = 1;
      let totalInterestAmount = 0;

      while (remainingDays > 0) {
         // Determine the days for this period (up to 30)
         const periodDays = Math.min(remainingDays, 30);

         // Get precise interest rate based on the days in this period
         const interestRate = getInterestRate(periodDays);

         // Calculate interest proportionally
         const periodInterestAmount = (amount * interestRate * periodDays) / 30;

         // Add to monthly breakdown
         monthlyBreakdown.push({
            month: currentMonth,
            days: periodDays,
            interestRate: interestRate * 100, // Convert to percentage
            interestAmount: periodInterestAmount,
         });

         // Update totals
         totalInterestAmount += periodInterestAmount;

         // Reduce remaining days
         remainingDays -= periodDays;
         currentMonth++;
      }

      return {
         totalInterestAmount,
         monthlyBreakdown,
      };
   }

   function getInterest() {
      return calculateCompoundInterest(pawn.value?.amount ?? 0, daysSincePawned.value ?? 0);
   }

   return {
      setPawn,
      today,
      pawnedDays,
      daysSincePawned,
      getInterest,
   };
}
