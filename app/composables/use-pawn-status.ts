import dayjs from 'dayjs';
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

/**
 * Composable for pawning status
 */
export function usePawnStatus(pawnRef: Ref<Pawn>) {
   /**
    * Get the pawning status, including unmarked
    */
   const pawnStatus = computed(() => {
      const pledgeDate = dayjs(pawnRef.value.pledge_date);

      if (dayjs().diff(pledgeDate, 'days') > 0 && pawnRef.value.state === 'ACTIVE') {
         return 'UNMARKED';
      }

      return pawnRef.value.state;
   });

   return {
      pawnStatus,
   };
}
