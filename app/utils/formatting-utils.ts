import dayjs, { Dayjs } from 'dayjs';

/**
 * Format number to include a thousand separator
 */
export function formatNumber(number: number) {
   return number.toLocaleString('en-US');
}

/**
 * Format number to be in Sri Lankan currency
 */
export function formatLKR(amount: number) {
   return new Intl.NumberFormat('en-LK', {
      style: 'currency',
      currency: 'LKR',
      currencyDisplay: 'narrowSymbol',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
   }).format(amount);
}

/**
 * Format number to be in SI weight standard
 */
export function formatWeight(value: number) {
   if (value > 1000) {
      return `${(value / 1000).toFixed(3)} kg`;
   }
   else {
      return `${value.toFixed(3)} g`;
   }
}

/**
 * Format the date string
 */
export function formatDateString(value: string) {
   return toDateString(dayjs(value));
}
