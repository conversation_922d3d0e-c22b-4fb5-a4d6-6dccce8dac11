<script setup lang="ts">
import { property } from 'es-toolkit/compat';
import type { Customer } from '~~/_backend/customers/types';

/* ---------------------------------------------------------------------------------------------- */

const { items } = defineProps<{
   items: Customer[];
}>();

const columns = [
   { key: 'since', label: 'Since', rowClass: 'w-20' },
   { key: 'customerName', label: 'Customer name' },
   { key: 'phone', label: 'Phone', rowClass: 'max-w-fit' },
   { key: 'nic', label: 'NIC' },
   { key: 'hasBills', label: 'Bills', rowClass: 'w-8' },
   { key: 'hasPawns', label: 'Pawns', rowClass: 'w-8' },
];

interface Row {
   id: number | string;
   customerName: string;
   address: string;
   phone: string;
   nic: string;
   since: string;
   hasBills: boolean;
   hasPawns: boolean;
}

const rows = ref<Row[]>([]);

watchDeep(
   () => items,
   (items) => {
      rows.value = [];

      items.forEach((item) => {
         rows.value.push({
            id: item.id,
            customerName: item.customer_name,
            address: item.address,
            phone: item.phone,
            nic: item.nic,
            since: formatDateString(item.since),
            hasBills: item.has_bills,
            hasPawns: item.has_pawns,
         });
      });
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div>
      <UTable
         :rows
         :columns
      >
         <!-- region: data -->
         <template #customerName-data="{ row }">
            <div>
               <p class="font-bold">
                  <UButton variant="link" :padded="false" :to="`/customers/${row.id}`">
                     {{ row.customerName }}
                  </UButton>
               </p>
               <p>{{ row.address }}</p>
            </div>
         </template>

         <template #hasBills-data="{ row }">
            <div class="flex items-center justify-center">
               <UIcon
                  v-if="row.hasBills"
                  class="text-emerald-500"
                  name="i-uil:check-circle"
                  size="24"
               />
               <div v-else></div>
            </div>
         </template>

         <template #hasPawns-data="{ row }">
            <div class="flex items-center justify-center">
               <UIcon
                  v-if="row.hasPawns"
                  class="text-emerald-500"
                  name="i-uil:check-circle"
                  size="24"
               />
               <div v-else></div>
            </div>
         </template>

         <!-- endregion: data -->
      </UTable>
   </div>
</template>

<style scoped lang="postcss"></style>
