<script setup lang="ts">
import type { ApiFetchCustomersQueryParams } from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const queryParamsModel = defineModel<ApiFetchCustomersQueryParams>({
   default: {
      query: '',
      page: 1,
      sort_by: 'since',
      sort_order: 'desc',
   },
});

const queryRef = ref('');

watchDebounced(
   queryRef,
   (value) => {
      queryParamsModel.value.query = value;
   },
   { debounce: 500 },
);

/* ---------------------------------------------------------------------------------------------- */

const sortOrder = [
   { id: 'asc', label: 'ASC', icon: 'i-uil:sort-amount-down' },
   { id: 'desc', label: 'DESC', icon: 'i-uil:sort-amount-up' },
];

const sortBy = [
   { id: 'id', label: 'ID' },
   { id: 'customer_name', label: 'Customer name' },
   { id: 'since', label: 'Since' },
];

/* ---------------------------------------------------------------------------------------------- */

const router = useRouter();
const route = useRoute();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <section class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex-1">
         <div class="flex">
            <UInput
               v-model="queryRef"
               placeholder="Search..."
            ></UInput>
         </div>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="flex gap-2">
         <USelectMenu
            v-model="queryParamsModel.sort_by"
            class="w-40"
            :options="sortBy"
            value-attribute="id"
            option-attribute="label"
         />
         <USelectMenu
            v-model="queryParamsModel.sort_order"
            class="w-28"
            :options="sortOrder"
            value-attribute="id"
            option-attribute="label"
         />
      </div>
      <!-- endregion: right -->
   </section>
</template>

<style scoped lang="postcss"></style>
