<script setup lang="ts">
import type { Customer } from '~~/_backend/customers/types';
import {
   type FetchCustomerPawnsQueryParams,
   useApiFetchCustomerBills,
   useApiFetchCustomerPawns,
} from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const { customer } = defineProps<{
   customer: Customer;
}>();

/* ---------------------------------------------------------------------------------------------- */

const query = reactive({
   customer_id: customer.id,
   page: 1,
} as FetchCustomerPawnsQueryParams);

const apiBills = reactive(useApiFetchCustomerBills(query));
apiBills.fetch();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div>
      <!-- region: listing -->
      <div class="mb-5">
         <div class="border rounded-2xl">
            <BillListingsTable :items="apiBills.bills" />
         </div>
      </div>

      <div v-if="apiBills.paginatedData">
         <p>There are {{ apiBills.paginatedData.total }} items(s)</p>

         <PaginationContainer
            v-model="query.page"
            :total="apiBills.paginatedData.total"
         />
      </div>

      <!-- endregion: listing -->
   </div>
</template>

<style scoped lang="postcss">

</style>
