<script setup lang="ts">
import type { Customer } from '~~/_backend/customers/types';
import {
   type FetchCustomerPawnsQueryParams,
   useApiFetchCustomerPawns,
} from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const { customer } = defineProps<{
   customer: Customer;
}>();

/* ---------------------------------------------------------------------------------------------- */

const query = reactive({
   customer_id: customer.id,
   page: 1,
   state: 'ACTIVE',
   sort_by: 'id',
   sort_order: 'asc',
} as FetchCustomerPawnsQueryParams);

const apiPawns = reactive(useApiFetchCustomerPawns(query));
apiPawns.fetch();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div>
      <div class="mb-5">
         <PawningListSummery
            v-if="apiPawns.paginatedData"
            :total-count="apiPawns.paginatedData.total"
            :total-collected="apiPawns.paginatedData.total_collected"
            :total-value="apiPawns.paginatedData.total_value"
            :total-amount="apiPawns.paginatedData.total_amount"
         />
      </div>

      <!-- region: pawning filters -->
      <div class="mb-5">
         <SingleCustomerPawningToolbar v-model="query" />
      </div>
      <!-- region: pawning filters -->

      <!-- region: listing -->
      <div class="mb-5">
         <div class="border rounded-2xl">
            <PawnListingsTable :items="apiPawns.pawns" />
         </div>
      </div>

      <div v-if="apiPawns.paginatedData">
         <p>There are {{ apiPawns.paginatedData.total }} items(s)</p>

         <PaginationContainer
            v-model="query.page"
            :total="apiPawns.paginatedData.total"
         />
      </div>

      <!-- endregion: listing -->
   </div>
</template>

<style scoped lang="postcss">

</style>
