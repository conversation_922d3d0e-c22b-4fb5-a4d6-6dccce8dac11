<script setup lang="ts">
import FindPawnById from '~/page-components/pawns/listings/find-pawn-by-id.vue';
import { todayAsString } from '~/utils/datetime-utils';
import type { FetchCustomerPawnsQueryParams } from '~~/_backend/customers/api';

/* ---------------------------------------------------------------------------------------------- */

const queryParams = defineModel<FetchCustomerPawnsQueryParams>({ default: {} });

const pawnDateRange = ref([todayAsString(), todayAsString()]);

watchDeep(pawnDateRange, ([start, end]) => {
   if (start && end) {
      queryParams.value.pawn_date_min = start;
      queryParams.value.pawn_date_max = end;
   }
});

const sortOrder = [
   {
      id: 'asc',
      label: 'Ascending',
   },
   {
      id: 'desc',
      label: 'Descending',
   },
];

const state = [
   { id: 'ALL', label: 'All' },
   { id: 'ACTIVE', label: 'Active' },
   { id: 'COLLECTED', label: 'Collected' },
   { id: 'EXPIRED', label: 'Expired' },
];

const dropdownState = ref('ALL');

watch(dropdownState, (state: any) => {
   if (state !== 'ALL') {
      queryParams.value.state = state;
   }
   else {
      queryParams.value.state = null;
   }
});

/* ---------------------------------------------------------------------------------------------- */

function clearFilters() {
   queryParams.value.pawn_date_min = '';
   queryParams.value.pawn_date_max = '';
   queryParams.value.collected_date_min = '';
   queryParams.value.collected_date_max = '';
   queryParams.value.pledge_date_min = '';
   queryParams.value.pledge_date_max = '';
   queryParams.value.weight_min = undefined;
   queryParams.value.weight_max = undefined;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <section class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex flex-1 flex-col items-center gap-2 lg:flex-row">
         <FindPawnById />

         <div class="flex items-center gap-1">
            <p class="shrink-0 text-xs">
               Pawning
            </p>
            <InputDateRange v-model="pawnDateRange" />
         </div>
         <SingleCustomerPawnsListingToolbarAdvancedFilters v-model="queryParams" />
         <UButton @click="clearFilters()">
            clear
         </UButton>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="flex gap-2">
         <USelectMenu
            v-model="queryParams.sort_order"
            :options="sortOrder"
            value-attribute="id"
            option-attribute="label"
         />
         <USelectMenu
            v-model="dropdownState"
            class="w-24"
            :options="state"
            value-attribute="id"
            option-attribute="label"
         />
      </div>
      <!-- endregion: right -->
   </section>
</template>

<style scoped lang="postcss"></style>
