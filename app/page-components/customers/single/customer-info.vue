<script setup lang="ts">
import type { Customer } from '~~/_backend/customers/types';
import { formatDateString } from '~/utils/formatting-utils';
/* ---------------------------------------------------------------------------------------------- */

const { customer } = defineProps<{
   customer: Customer;
}>();
</script>

<template>
   <div class="">
      <div>
         <header class="mb-5">
            <h2 class="text-2xl lg:text-3xl font-bold">
               {{ customer.customer_name }}
            </h2>

            <p class="uppercase text-sm text-gray-500">
               Since {{ formatDateString(customer.since) }}
            </p>
         </header>

         <!-- region: details -->
         <div class="flex flex-col lg:items-center gap-1 text-gray-500 lg:flex-row lg:gap-5">
            <div class="flex items-center gap-1">
               <UIcon name="i-uil:user-square" />
               <p>{{ customer.nic || '-' }}</p>
            </div>

            <div class="flex items-center gap-1">
               <UIcon name="i-uil:location-point" />
               <p>{{ customer.address || '-' }}</p>
            </div>

            <div class="flex items-center gap-1">
               <UIcon name="i-uil:phone" />
               <p>{{ customer.phone || '-' }}</p>
            </div>

            <div class="flex items-center gap-1">
               <UIcon name="i-uil:envelope" />
               <p>{{ customer.email || '-' }}</p>
            </div>
         </div>
         <!-- endregion: details -->
      </div>
   </div>
</template>

<style scoped lang="postcss"></style>
