<script setup lang="ts">
import { z } from 'zod';
import { SupplierSchema } from '~~/_backend/suppliers/types';

/* ---------------------------------------------------------------------------------------------- */

const isOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   supplier_name: z.string().min(1, 'Supplier name is required'),
});

const formState = reactive({
   supplier_name: '',
   address: '',
   phone: '',
   mobile: '',
   email: '',
});

const isSubmitting = ref(false);
const submitErrors = ref('');

/**
 * <PERSON><PERSON> submit button
 */
async function handleSubmit() {
   isSubmitting.value = true;
   submitErrors.value = '';

   try {
      const response = await useNuxtApp().$api('/supplier/create', {
         method: 'POST',
         body: formState,
      });

      const createdSupplier = SupplierSchema.parse(response);

      isOpen.value = false;

      return navigateTo(`/suppliers/${createdSupplier.id}`);
   }
   catch (error) {
      console.error(error);
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton @click="isOpen = true">
      Create supplier
   </UButton>

   <UModal
      v-model="isOpen" prevent-close :ui="{
         width: 'sm:max-w-xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <header class="flex items-center justify-between">
               <Heading3>Create supplier</Heading3>
            </header>
         </template>

         <section>
            <UForm
               class="flex flex-col gap-5"
               :schema="formSchema"
               :state="formState"
               @submit="handleSubmit"
            >
               <!-- region: form -->
               <div class="grid">
                  <UFormGroup
                     label="Supplier name"
                     name="supplier_name"
                     required
                  >
                     <UInput v-model="formState.supplier_name" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup label="Address">
                     <UTextarea v-model="formState.address" />
                  </UFormGroup>
               </div>

               <div class="grid grid-cols-2 gap-3">
                  <UFormGroup label="Phone">
                     <UInput v-model="formState.phone" />
                  </UFormGroup>

                  <UFormGroup label="Mobile">
                     <UInput v-model="formState.mobile" />
                  </UFormGroup>
               </div>

               <div class="grid">
                  <UFormGroup label="Email">
                     <UInput v-model="formState.email" />
                  </UFormGroup>
               </div>

               <!-- endregion: form -->

               <footer class="flex justify-end gap-3">
                  <UButton
                     type="submit"
                     :loading="isSubmitting"
                  >
                     Create
                  </UButton>

                  <UButton
                     color="gray"
                     icon="i-uil:times"
                     @click="isOpen = false"
                  >
                     Close
                  </UButton>
               </footer>
            </UForm>
         </section>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss">

</style>
