<script setup lang="ts">
import type { Supplier } from '~~/_backend/suppliers/types';
import EditSupplier from '~/page-components/suppliers/edit-supplier.vue';

/* ---------------------------------------------------------------------------------------------- */

const { supplier } = defineProps<{
   supplier: Supplier;
}>();

const emits = defineEmits(['update']);
</script>

<template>
   <div class="flex flex-col lg:flex-row gap-5 w-full">
      <section class="flex-1">
         <Heading1 class="flex items-center gap-2">
            <UIcon name="i-fa6-solid:address-card" />
            {{ supplier.supplier_name }}
         </Heading1>

         <Heading3 class="flex items-center gap-2">
            <UIcon name="i-fa6-solid:location-dot" />
            {{ supplier.address }}
         </Heading3>

         <div class="flex gap-5 items-center text-secondaryColor-500">
            <div class="flex items-center gap-2">
               <UIcon name="i-fa6-solid:phone" />
               <p v-if="supplier.phone">
                  {{ supplier.phone }}
               </p>
               <p v-else class="text-secondaryColor-300">
                  No phone
               </p>
            </div>

            <div class="flex items-center gap-2">
               <UIcon name="i-fa6-solid:mobile-retro" />
               <p v-if="supplier.mobile">
                  {{ supplier.mobile }}
               </p>
               <p v-else class="text-secondaryColor-300">
                  No mobile
               </p>
            </div>

            <div class="flex items-center gap-2">
               <UIcon name="i-fa6-solid:envelope" />
               <p v-if="supplier.email">
                  {{ supplier.email }}
               </p>
               <p v-else class="text-secondaryColor-300">
                  No email
               </p>
            </div>
         </div>
      </section>

      <aside>
         <EditSupplier :supplier="supplier" @update="$emit('update')" />
      </aside>
   </div>
</template>

<style scoped lang="postcss"></style>
