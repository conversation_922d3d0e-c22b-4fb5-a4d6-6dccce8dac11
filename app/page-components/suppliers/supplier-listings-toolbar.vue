<script setup lang="ts">
import CreateNewSupplierModal from '~/page-components/suppliers/create-new-supplier-modal.vue';
</script>

<template>
   <div class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex-1">
         <div class="flex">
            <UInput placeholder="Search suppliers..." />
         </div>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="">
         <CreateNewSupplierModal />
      </div>
      <!-- endregion: right -->
   </div>
</template>

<style scoped lang="postcss">

</style>
