<script setup lang="ts">
import { z } from 'zod';
import type { Supplier } from '~~/_backend/suppliers/types';

const { supplier } = defineProps<{
   supplier: Supplier;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const isOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   supplier_name: z.string().trim().min(2, 'Supplier name is required'),
});

const formState = reactive({
   id: supplier.id,
   supplier_name: '',
   address: '',
   phone: '',
   mobile: '',
   email: '',
});

const isSubmitting = ref(false);
const submitErrors = ref('');

async function handleSubmit() {
   isSubmitting.value = true;
   submitErrors.value = '';

   try {
      await useNuxtApp().$api('/supplier/update', {
         method: 'PATCH',
         body: formState,
      });

      emit('update');
      isOpen.value = false;
   }
   catch (error) {
      console.error(error);
      submitErrors.value = 'Failed to update supplier details';
   }

   isSubmitting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */

watchDeep(
   () => supplier,
   (supplier) => {
      formState.id = supplier.id;
      formState.supplier_name = supplier.supplier_name;
      formState.address = supplier.address;
      formState.phone = supplier.phone;
      formState.mobile = supplier.mobile;
      formState.email = supplier.email;
   },
   { immediate: true },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton
      variant="soft"
      color="orange"
      icon="i-uil:pen"
      @click="isOpen = true"
   >
      EDIT
   </UButton>

   <UModal
      v-model="isOpen"
      prevent-close
      :ui="{
         width: 'sm:max-w-2xl',
         padding: 'sm:p-4',
      }"
   >
      <UCard>
         <template #header>
            <Heading2>Edit supplier details</Heading2>
         </template>

         <!-- region: form -->
         <UForm
            class="flex flex-col gap-3"
            :schema="formSchema"
            :state="formState"
            @submit="handleSubmit"
         >
            <div class="grid grid-cols-1">
               <UFormGroup
                  label="Supplier name"
                  name="supplier_name"
               >
                  <UInput v-model="formState.supplier_name" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-1">
               <UFormGroup label="Address">
                  <UTextarea v-model="formState.address" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Phone">
                  <UInput v-model.trim="formState.phone" />
               </UFormGroup>

               <UFormGroup label="Mobile">
                  <UInput v-model.trim="formState.mobile" />
               </UFormGroup>
            </div>

            <div class="grid">
               <UFormGroup label="Email">
                  <UInput v-model.trim="formState.email" />
               </UFormGroup>
            </div>

            <footer class="flex w-full justify-end gap-3">
               <UButton
                  type="submit"
                  :loading="isSubmitting"
               >
                  Update
               </UButton>
               <UButton
                  color="gray"
                  @click="isOpen = false"
               >
                  Cancel
               </UButton>
            </footer>

            <!-- region: Errors -->
            <div v-if="submitErrors" class="mt-5">
               <AlertError
                  :description="submitErrors"
               ></AlertError>
            </div>
            <!-- endregion: Errors -->
         </UForm>
         <!-- endregion: form -->
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
