<script setup lang="ts">
import { z } from 'zod';

/* ---------------------------------------------------------------------------------------------- */

const isOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   title: z.string().min(1),
   description: z.string(),
});

const formState = reactive({
   title: '',
   description: '',
});

const isCreating = ref(false);
const createError = ref('');

/**
 * Handles create
 */
async function handleCreate() {
   isCreating.value = true;

   try {
      const response = await useNuxtApp().$api<{ id: number }>('/stock-category/create', {
         method: 'POST',
         body: formState,
      });

      isOpen.value = false;

      return navigateTo(`/categories/${response.id}`);
   }
   catch (error) {
      console.error(error);

      createError.value = 'Failed to create the category. Make sure it is not a duplicate.';
   }

   isCreating.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton @click="isOpen = true">
      Create category
   </UButton>

   <UModal
      v-model="isOpen"
      prevent-close
   >
      <UCard>
         <template #header>
            <Heading2>Create new category</Heading2>
         </template>

         <UForm
            class="flex flex-col gap-5"
            :schema="formSchema"
            :state="formState"
            @submit="handleCreate()"
         >
            <div>
               <UFormGroup
                  label="Title"
                  name="title"
               >
                  <UInput v-model="formState.title" />
               </UFormGroup>
            </div>

            <div>
               <UFormGroup
                  label="Description"
                  name="description"
               >
                  <UTextarea
                     v-model="formState.description"
                     placeholder="Optional"
                  />
               </UFormGroup>
            </div>

            <AlertError
               v-if="createError"
               :description="createError"
            />

            <footer class="flex justify-end gap-3">
               <UButton
                  type="submit"
                  :loading="isCreating"
               >
                  Create
               </UButton>
               <UButton
                  type="button"
                  color="gray"
                  icon="i-uil:times"
                  @click="isOpen = false"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
