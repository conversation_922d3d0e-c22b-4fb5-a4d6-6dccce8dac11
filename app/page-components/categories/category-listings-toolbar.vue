<script setup lang="ts">
/* ---------------------------------------------------------------------------------------------- */

const emit = defineEmits(['create']);

/* ---------------------------------------------------------------------------------------------- */

const queryParams = defineModel({
   default: {
      page: 1,
      query: '',
   },
});

const txtQuery = ref('');

watchDebounced(
   txtQuery,
   (value) => {
      queryParams.value.query = value;
   },
   { debounce: 500 },
);
</script>

<template>
   <div class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex-1">
         <div class="flex">
            <UInput
               v-model="txtQuery"
               placeholder="Search categories..."
            />
         </div>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div>
         <CreateNewCagetoryModal />
      </div>
      <!-- endregion: right -->
   </div>
</template>

<style scoped lang="postcss"></style>
