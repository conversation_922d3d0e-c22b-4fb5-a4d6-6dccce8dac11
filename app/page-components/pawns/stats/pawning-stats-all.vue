<script setup lang="ts">
import { useApiFetchPawningStats } from '~~/_backend/pawning/api';
import { formatLKR, formatNumber } from '~/utils/formatting-utils';
import StatsGroup from '~/components/stats/stats-group.vue';

/* ---------------------------------------------------------------------------------------------- */

const { title, customRange = false } = defineProps<{
   title: string;
   customRange?: boolean;
}>();

/* ---------------------------------------------------------------------------------------------- */

const minDatRef = defineModel<string | null>('dateMin', { default: null });
const maxDateRef = defineModel<string | null>('dateMax', { default: null });

const api = reactive(useApiFetchPawningStats(minDatRef, maxDateRef));

if (!customRange)
   api.fetch();

/* ---------------------------------------------------------------------------------------------- */

const inputDateRangeValue = ref([todayAsString(), todayAsString()]);

watchDeep(inputDateRangeValue, ([min, max]) => {
   if (min && max) {
      minDatRef.value = min;
      maxDateRef.value = max;

      api.fetch();
   }
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div>
      <header>
         <h1 class="mb-5 text-4xl font-bold text-secondaryColor-400">
            {{ title }}
         </h1>
      </header>

      <!-- region: custom date range -->
      <section
         v-if="customRange"
         class="mb-5 flex"
      >
         <div>
            <InputDateRange v-model="inputDateRangeValue" />
         </div>
      </section>
      <!-- endregion: custom date range -->

      <div
         v-if="api.pawningStats"
         class="flex"
      >
         <StatsGroup>
            <StatItem
               size="sm"
               footer
            >
               <template #title>
                  Created pawns
               </template>
               <template #value>
                  {{ formatLKR(api.pawningStats.created_total) }}
               </template>
               <template #footer>
                  {{ formatNumber(api.pawningStats.count) }} items
               </template>
            </StatItem>

            <StatItem
               footer
               size="sm"
            >
               <template #title>
                  Active pawns
               </template>
               <template #value>
                  {{ formatLKR(api.pawningStats.active_total) }}
               </template>
               <template #footer>
                  {{ formatNumber(api.pawningStats.active) }} items
               </template>
            </StatItem>
            <StatItem
               footer
               size="sm"
               color="green"
            >
               <template #title>
                  Collected pawns
               </template>
               <template #value>
                  {{ formatLKR(api.pawningStats.collected_total) }}
               </template>
               <template #footer>
                  {{ formatNumber(api.pawningStats.collected) }} items
               </template>
            </StatItem>

            <StatItem
               footer
               size="sm"
               color="blue"
            >
               <template #title>
                  Total amount
               </template>
               <template #value>
                  {{ formatLKR(api.pawningStats.amount_total) }}
               </template>
               <template #footer>
                  {{ formatLKR(api.pawningStats.collected_total - api.pawningStats.amount_total) }}
               </template>
            </StatItem>

            <StatItem
               footer
               size="sm"
               color="orange"
            >
               <template #title>
                  Expired pawns
               </template>
               <template #value>
                  {{ formatLKR(api.pawningStats.expired_total) }}
               </template>
               <template #footer>
                  {{ formatNumber(api.pawningStats.expired) }} items
               </template>
            </StatItem>
         </StatsGroup>
      </div>
   </div>
</template>

<style scoped lang="postcss"></style>
