<script setup lang="ts">
import { usePawnListingsStore } from '~/stores/pawn-listings-store';

const pawnListingStore = usePawnListingsStore();
const { queryParams } = storeToRefs(pawnListingStore);

/* ---------------------------------------------------------------------------------------------- */

const collectedDateRange = ref([todayAsString(), todayAsString()]);
const pledgeDateRange = ref([todayAsString(), todayAsString()]);

function filterByCollectionDates() {
   queryParams.value.pawn_date_min = '';
   queryParams.value.pawn_date_max = '';
   queryParams.value.pledge_date_min = '';
   queryParams.value.pledge_date_max = '';

   queryParams.value.collected_date_min = collectedDateRange.value[0] ?? '';
   queryParams.value.collected_date_max = collectedDateRange.value[1] ?? '';
}

function filterByPledgeDates() {
   queryParams.value.pawn_date_min = '';
   queryParams.value.pawn_date_max = '';
   queryParams.value.pledge_date_min = pledgeDateRange.value[0] ?? '';
   queryParams.value.pledge_date_max = pledgeDateRange.value[1] ?? '';

   queryParams.value.collected_date_min = '';
   queryParams.value.collected_date_max = '';
}

/* ---------------------------------------------------------------------------------------------- */

const weightMin = ref<number | undefined>(undefined);
const weightMax = ref<number | undefined>(undefined);

function filterByWeight() {
   queryParams.value.weight_min = weightMin.value;
   queryParams.value.weight_max = weightMax.value;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UPopover>
      <UButton
         icon="i-uil:filter"
         color="green"
      ></UButton>

      <template #panel>
         <div class="flex flex-col gap-5 p-4">
            <div class="">
               <p class="shrink-0 text-xs">
                  Collected
               </p>
               <div class="flex gap-1">
                  <InputDateRange v-model="collectedDateRange" />
                  <UButton
                     icon="i-uil:check"
                     @click="filterByCollectionDates()"
                  ></UButton>
               </div>
            </div>

            <div class="">
               <p class="shrink-0 text-xs">
                  Pledge
               </p>
               <div class="flex gap-1">
                  <InputDateRange v-model="pledgeDateRange" allow-future />
                  <UButton
                     icon="i-uil:check"
                     @click="filterByPledgeDates()"
                  ></UButton>
               </div>
            </div>

            <div>
               <p class="shrink-0 text-xs">
                  Weight
               </p>

               <div class="flex gap-1">
                  <UInput v-model="weightMin" placeholder="Min" />
                  <UInput v-model="weightMax" placeholder="Max" />
                  <UButton
                     icon="i-uil:check"
                     @click="filterByWeight()"
                  />
               </div>
            </div>
         </div>
      </template>
   </UPopover>
</template>

<style scoped lang="postcss"></style>
