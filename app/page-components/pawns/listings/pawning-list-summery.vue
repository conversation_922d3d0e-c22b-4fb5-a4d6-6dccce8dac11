<script setup lang="ts">
const { totalCount, totalAmount, totalCollected, totalValue } = defineProps<{
   totalCount: number;
   totalAmount: number;
   totalCollected: number;
   totalValue: number;
}>();
</script>

<template>
   <div>
      <StatsGroup>
         <StatItem size="sm">
            <template #title>
               Total
            </template>
            <template #value>
               {{ totalCount }}
            </template>
         </StatItem>

         <StatItem size="sm">
            <template #title>
               Amount
            </template>
            <template #value>
               {{ formatLKR(totalAmount) }}
            </template>
         </StatItem>

         <StatItem size="sm">
            <template #title>
               Collected
            </template>
            <template #value>
               {{ formatLKR(totalCollected) }}
            </template>
         </StatItem>

         <StatItem size="sm">
            <template #title>
               Collected interest
            </template>
            <template #value>
               {{ formatLKR(totalCollected - totalAmount) }}
            </template>
         </StatItem>

         <StatItem size="sm">
            <template #title>
               Value
            </template>
            <template #value>
               {{ formatLKR(totalValue) }}
            </template>
         </StatItem>
      </StatsGroup>
   </div>
</template>

<style scoped lang="postcss">

</style>
