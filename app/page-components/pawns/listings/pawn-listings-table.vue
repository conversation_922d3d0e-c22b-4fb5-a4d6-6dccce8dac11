<script setup lang="ts">
import dayjs from 'dayjs';
import type { Pawn } from '~~/_backend/pawning/types';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

const { items } = defineProps<{
   items: Pawn[];
}>();

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'id', label: 'ID' },
   { key: 'dates', label: 'Date' },
   { key: 'customer', label: 'Customer' },
   { key: 'weight', label: 'Weight' },
   { key: 'karat', label: 'Kt' },
   { key: 'amount', label: 'Amount' },
   { key: 'collectedAmount', label: 'Collected' },
   { key: 'colExpDate', label: 'Col/Exp Date' },
   { key: 'state', label: 'State' },
];

interface Row {
   id: number | string;
   state: string;
   customer: string;
   weight: number;
   karat: string;
   amount: number;
   total: number;
   interestAmount: number;
   discount: number;
   collectedAmount: number;
   collectedDate: string;
   expiredDate: string;
   class: string;
   pawnDate: string;
   pledgeDate: string;
}

const rows = ref<Row[]>([]);

watchDeep(
   () => items,
   (items) => {
      rows.value = [];

      const setBgColor = (state: 'ACTIVE' | 'EXPIRED' | 'COLLECTED' | 'UNMARKED') => {
         switch (state) {
            case 'ACTIVE':
               return 'bg-green-50/50';
            case 'EXPIRED':
               return 'bg-red-50/50';
            case 'COLLECTED':
               return 'bg-slate-50/50';
            case 'UNMARKED':
               return 'bg-amber-50/50';
         }
      };

      function getState(pledgeDate: string, state: any) {
         if (state === 'ACTIVE' && dayjs(pledgeDate).diff(dayjs(), 'days') < 0) {
            return 'UNMARKED';
         }
         else {
            return state;
         }
      }

      items.forEach((item) => {
         const state = getState(item.pledge_date, item.state);

         rows.value.push({
            id: item.id,
            state,
            customer: item.customer.customer_name,
            weight: item.weight,
            karat: item.karat,
            amount: item.amount,
            total: item.amount + item.interest_amount,
            interestAmount: item.interest_amount,
            discount: item.discount,
            collectedAmount: item.collected_amount ?? 0,
            collectedDate: item.collected_date ?? '',
            expiredDate: item.expired_date ?? '',
            class: setBgColor(state),
            pawnDate: item.pawn_date,
            pledgeDate: item.pledge_date,
         });
      });
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UTable
      :columns="columns"
      :rows="rows"
   >
      <!-- region: headers -->
      <template #weight-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #amount-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #total-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #collectedAmount-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #colExpDate-header="{ column }">
         <div class="text-center">
            {{ column.label }}
         </div>
      </template>

      <template #state-header="{ column }">
         <div class="text-center">
            {{ column.label }}
         </div>
      </template>

      <!-- endregion: headers -->

      <template #id-data="{ row }">
         <div>
            <UButton
               class="px-2"
               size="sm"
               :padded="false"
               :to="`/pawns/${row.id}`"
            >
               {{ row.id }}
            </UButton>
         </div>
      </template>

      <template #dates-data="{ row }">
         <div class="text-xs">
            <p>{{ row.pawnDate }}</p>
            <p>{{ row.pledgeDate }}</p>
         </div>
      </template>

      <template #customer-data="{ row }">
         <div class="uppercase font-bold">
            {{ row.customer }}
         </div>
      </template>

      <template #weight-data="{ row }">
         <div class="text-right">
            {{ formatWeight(row.weight) }}
         </div>
      </template>

      <template #amount-data="{ row }">
         <div class="text-right">
            <p>{{ formatLKR(row.amount) }}</p>
         </div>
      </template>

      <template #collectedAmount-data="{ row }">
         <div
            v-if="row.state !== 'ACTIVE'"
            class="text-right"
         >
            {{ formatLKR(row.collectedAmount) }}
         </div>
         <div
            v-else
            class="text-right"
         >
            -
         </div>
      </template>

      <template #colExpDate-data="{ row }">
         <div class="text-center">
            <template v-if="row.collectedDate">
               {{ row.collectedDate }}
            </template>
            <template v-if="row.expredDate">
               {{ row.expiredDate }}
            </template>
         </div>
      </template>

      <template #state-data="{ row }">
         <div class="text-center">
            <template v-if="row.state === 'ACTIVE'">
               <UBadge
                  class="text-xs uppercase"
                  size="xs"
                  color="green"
                  variant="outline"
               >
                  Active
               </UBadge>
            </template>

            <template v-if="row.state === 'COLLECTED'">
               <UBadge
                  class="text-xs uppercase"
                  size="xs"
                  color="gray"
                  variant="outline"
               >
                  Collected
               </UBadge>
            </template>

            <template v-if="row.state === 'EXPIRED'">
               <UBadge
                  class="text-xs uppercase"
                  size="xs"
                  color="red"
                  variant="outline"
               >
                  Expired
               </UBadge>
            </template>

            <template v-if="row.state === 'UNMARKED'">
               <UBadge
                  class="text-xs uppercase"
                  size="xs"
                  color="amber"
                  variant="outline"
               >
                  Unmarked
               </UBadge>
            </template>
         </div>
      </template>
   </UTable>
</template>

<style scoped lang="postcss"></style>
