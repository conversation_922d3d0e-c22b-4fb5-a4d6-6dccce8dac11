<script setup lang="ts">
import { useApiFetchPawn } from '~~/_backend/pawning/api';

/* ---------------------------------------------------------------------------------------------- */

const toast = useToast();

const pawnId = ref();
const pawnIdInput = ref<number>(0);

const apiPawn = reactive(useApiFetchPawn(pawnIdInput));

watchImmediate(pawnId, (id) => {
   if (id) {
      pawnIdInput.value = Number(id);
   }
   else {
      pawnIdInput.value = Number(0);
   }
});

async function find() {
   await apiPawn.fetch();

   if (!apiPawn.pawn) {
      toast.add({
         title: 'Pawn',
         description: 'No pawn found',
         color: 'red',
      });
   }
   else {
      return navigateTo(`/pawns/${pawnId.value}`);
   }
}
</script>

<template>
   <div class="flex gap-1">
      <UInput v-model="pawnId" placeholder="Enter ID" />
      <UButton :disabled="!pawnId" @click="find()">
         Go
      </UButton>
   </div>
</template>

<style scoped lang="postcss">

</style>
