<script setup lang="ts">
import { usePawnListingsStore } from '~/stores/pawn-listings-store';
import { todayAsString } from '~/utils/datetime-utils';
import FindPawnById from '~/page-components/pawns/listings/find-pawn-by-id.vue';

const props = withDefaults(defineProps<Props>(), {
   showPrintButton: true,
});
/* ---------------------------------------------------------------------------------------------- */

const pawnListingStore = usePawnListingsStore();
const { queryParams } = storeToRefs(pawnListingStore);

/* ---------------------------------------------------------------------------------------------- */
/*
 * Toolbar fields
 */

const pawnDateRange = ref([todayAsString(), todayAsString()]);

watchDeep(pawnDateRange, ([start, end]) => {
   if (start && end) {
      queryParams.value.pawn_date_min = start;
      queryParams.value.pawn_date_max = end;
   }
});

/* ---------------------------------------------------------------------------------------------- */

const sortOrder = [
   {
      id: 'asc',
      label: 'Ascending',
   },
   {
      id: 'desc',
      label: 'Descending',
   },
];

const sortBy = [
   { id: 'id', label: 'ID' },
   { id: 'customer_id', label: 'Customer' },
   { id: 'value', label: 'Value' },
   { id: 'weight', label: 'Weight' },
   { id: 'amount', label: 'Amount' },
   { id: 'pawn_date', label: 'Pawn Date' },
   { id: 'pledge_date', label: 'Pledge Date' },
   { id: 'state', label: 'State' },
];

const state = [
   { id: 'ALL', label: 'All' },
   { id: 'ACTIVE', label: 'Active' },
   { id: 'COLLECTED', label: 'Collected' },
   { id: 'EXPIRED', label: 'Expired' },
];

const dropdownState = ref('ALL');

watch(dropdownState, (state: any) => {
   if (state !== 'ALL') {
      queryParams.value.state = state;
   }
   else {
      queryParams.value.state = null;
   }
});

/* ---------------------------------------------------------------------------------------------- */
const route = useRoute();
const router = useRouter();

/*
 * Set the initial values from the route query
 */

const {
   state: qState,
   sort_order: qSortOrder,
   sort_by: qSortBy,
   collected_date_min: qCollectedDateMin,
   collected_date_max: qCollectedDateMax,
   pledge_date_min: qPledgeDateMin,
   pledge_date_max: qPledgeDateMax,
   pawn_date_min: qPawnDateMin,
   pawn_date_max: qPawnDateMax,
} = route.query;

if (qState) {
   queryParams.value.state = qState as any;
   dropdownState.value = qState as any;
}
else {
   queryParams.value.state = null;
   dropdownState.value = 'ALL';
}

if (qSortOrder) {
   queryParams.value.sort_order = String(qSortOrder) as any;
}

if (qSortBy) {
   queryParams.value.sort_by = String(qSortBy) as any;
}

if (qCollectedDateMin && qCollectedDateMax) {
   queryParams.value.collected_date_min = String(qCollectedDateMin) as any;
   queryParams.value.collected_date_max = String(qCollectedDateMax) as any;
}

if (qPawnDateMin && qPawnDateMax) {
   queryParams.value.pawn_date_min = String(qPawnDateMin) as any;
   queryParams.value.pawn_date_max = String(qPawnDateMax) as any;
}

if (qPledgeDateMin && qPledgeDateMax) {
   queryParams.value.pledge_date_min = String(qPledgeDateMin) as any;
   queryParams.value.pledge_date_max = String(qPledgeDateMax) as any;
}

watch(
   queryParams,
   async (params) => {
      await router.push({
         query: params,
      });
   },
   { immediate: false, deep: true },
);

/* ---------------------------------------------------------------------------------------------- */

function clearFilters() {
   queryParams.value.pawn_date_min = '';
   queryParams.value.pawn_date_max = '';
   queryParams.value.collected_date_min = '';
   queryParams.value.collected_date_max = '';
   queryParams.value.pledge_date_min = '';
   queryParams.value.pledge_date_max = '';
   queryParams.value.weight_min = undefined;
   queryParams.value.weight_max = undefined;
   queryParams.value.sort_by = 'pawn_date';
   queryParams.value.sort_order = 'desc';
}

/* ---------------------------------------------------------------------------------------------- */

interface Props {
   showPrintButton?: boolean;
}
</script>

<template>
   <section class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex flex-1 flex-col items-center gap-2 lg:flex-row">
         <FindPawnById />

         <div class="flex items-center gap-1">
            <p class="shrink-0 text-xs">
               Pawning
            </p>
            <InputDateRange v-model="pawnDateRange" />
         </div>
         <PawnsListingToolbarAdvancedFilters />
         <UButton @click="clearFilters()">
            clear
         </UButton>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="flex gap-2">
         <USelectMenu
            v-model="queryParams.sort_by"
            class="w-32"
            :options="sortBy"
            value-attribute="id"
            option-attribute="label"
         />
         <USelectMenu
            v-model="queryParams.sort_order"
            :options="sortOrder"
            value-attribute="id"
            option-attribute="label"
         />
         <USelectMenu
            v-model="dropdownState"
            class="w-24"
            :options="state"
            value-attribute="id"
            option-attribute="label"
         />
         <UButton
            v-if="showPrintButton"
            to="/pawns/print"
            target="_blank"
            icon="heroicons:printer"
         >
            Print List
         </UButton>
      </div>
      <!-- endregion: right -->
   </section>
</template>

<style scoped lang="postcss"></style>
