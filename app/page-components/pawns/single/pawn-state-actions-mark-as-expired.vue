<script setup lang="ts">
import { z } from 'zod';
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const isOpenMarkAsExpired = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   expired_date: z.string().date('Needs to be a valid date'),
});

const formState = reactive({
   pawn_id: pawn.id,
   expired_date: pawn.pledge_date,
});

/**
 * Handle set as expired
 */
async function handleMarkAsExpired() {
   try {
      await useNuxtApp().$api('/pawn/update/state', {
         method: 'PATCH',
         body: {
            pawn_id: pawn.id,
            expired_date: formState.expired_date,
            state: 'EXPIRED',
         },
      });

      emit('update');
   }
   catch (error) {
      console.error(error);
   }
}
</script>

<template>
   <UButton color="red" @click="isOpenMarkAsExpired = true">
      Mark as expired
   </UButton>

   <!-- region: Modal -->
   <UModal
      v-model="isOpenMarkAsExpired"
      prevent-close
   >
      <div class="flex flex-col">
         <header class="px-5 pt-5">
            <Heading3>Mark as expired</Heading3>
         </header>
         <UDivider />

         <section class="p-5">
            <p>Are you sure to set as expired?</p>

            <div class="flex my-2">
               <UFormGroup label="Expired date">
                  <InputDate v-model="formState.expired_date" />
               </UFormGroup>
            </div>

            <div class="flex justify-end gap-3">
               <UButton
                  color="red"
                  @click="handleMarkAsExpired()"
               >
                  Mark as expired
               </UButton>
               <UButton
                  color="gray"
                  @click="isOpenMarkAsExpired = false"
               >
                  Cancel
               </UButton>
            </div>
         </section>
      </div>
   </UModal>
   <!-- endregion: Modal -->
</template>

<style scoped lang="postcss">

</style>
