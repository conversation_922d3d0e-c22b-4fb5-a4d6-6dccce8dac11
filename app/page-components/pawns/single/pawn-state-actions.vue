<script setup lang="ts">
import { z } from 'zod';
import type { Pawn } from '~~/_backend/pawning/types';
import PawnStateActionsSetMarkAsCollected from '~/page-components/pawns/single/pawn-state-actions-set-mark-as-collected.vue';
import PawnStateActionsMarkAsExpired from '~/page-components/pawns/single/pawn-state-actions-mark-as-expired.vue';
import PawnStateActionsExtendPawnPeriod from '~/page-components/pawns/single/pawn-state-actions-extend-pawn-period.vue';
import PawnStateActionsEnablePawn from '~/page-components/pawns/single/pawn-state-actions-enable-pawn.vue';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */
/*
 * Set as expired
 */

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div class="flex items-center justify-end gap-6">
      <PawnStateActionsSetMarkAsCollected
         :pawn="pawn"
         @update="emit('update')"
      />

      <!-- region: Admin only options -->
      <section
         v-if="user?.isAdmin"
         class="flex items-center gap-3"
      >
         <!-- region: When the pawn is active -->
         <template v-if="pawn.state === 'ACTIVE'">
            <PawnStateActionsMarkAsExpired
               :pawn="pawn"
               @update="emit('update')"
            />

            <PawnStateActionsExtendPawnPeriod
               :pawn="pawn"
               @update="emit('update')"
            />
         </template>
         <!-- endregion: When the pawn is active -->

         <!-- region: Re-activate the pawn -->
         <template v-if="pawn.state === 'EXPIRED'">
            <PawnStateActionsEnablePawn
               :pawn="pawn"
               @update="emit('update')"
            />
         </template>
         <!-- endregion: Re-activate the pawn -->

         <!-- region: When the pawn is collected -->
         <template v-if="pawn.state === 'COLLECTED'">
            <UButton
               color="red"
               icon="i-uil:trash-alt"
            >
               Delete this pawn
            </UButton>
         </template>
         <!-- endregion: When the pawn is collected -->
      </section>

      <!-- endregion: Admin only options -->
   </div>
</template>

<style scoped lang="postcss"></style>
