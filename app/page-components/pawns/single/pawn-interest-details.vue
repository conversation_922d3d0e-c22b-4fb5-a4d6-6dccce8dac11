<script setup lang="ts">
import dayjs from 'dayjs';
import type { Pawn } from '~~/_backend/pawning/types';
import { useEnvInterestRates, usePawnInterestCalc } from '~/composables/use-pawn-interest-calc';
import { formatLKR } from '~/utils/formatting-utils';
import { usePawnStatus } from '~/composables/use-pawn-status';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

/* ---------------------------------------------------------------------------------------------- */

const { setPawn, pawnedDays, daysSincePawned, getInterest } = usePawnInterestCalc();

/* ---------------------------------------------------------------------------------------------- */

watchImmediate(
   () => pawn,
   (pawn) => {
      setPawn(pawn);
   },
);

const pledgeDateRelativeToToday = dayjs(pawn.pledge_date).diff(dayjs(), 'days');
const pledgeDateRelativeToTodayString = useTimeAgo(pawn.pledge_date);

/* ---------------------------------------------------------------------------------------------- */

const interestRates = useEnvInterestRates();

const { totalInterestAmount, monthlyBreakdown } = getInterest();

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div class="text-center">
      <div class="mb-5">
         <div class="text-2xl">
            Pawned for {{ pawnedDays }} days
         </div>

         <div>Days since pawned {{ daysSincePawned }}</div>
      </div>

      <!-- region: Pledge state -->
      <div class="flex w-full justify-center">
         <div
            class="rounded-xl border px-5 py-2"
            :class="[
               pledgeDateRelativeToToday > 0
                  ? 'border-emerald-100 bg-emerald-50'
                  : 'border-red-100 bg-red-50',
            ]"
         >
            <header
               class="mb-2 text-sm font-bold uppercase"
               :class="[pledgeDateRelativeToToday > 0 ? 'text-emerald-500' : 'text-red-500']"
            >
               Collection date
            </header>
            <p
               class="text-2xl"
               :class="[pledgeDateRelativeToToday > 0 ? 'text-emerald-500' : 'text-red-500']"
            >
               {{ pledgeDateRelativeToTodayString }} ({{ pledgeDateRelativeToToday }} days)
            </p>
         </div>
      </div>
      <!-- endregion: Pledge state -->

      <UDivider class="my-3">
         Interest breakdown
      </UDivider>

      <div class="mb-5 flex justify-center">
         <StatsGroup>
            <StatItem>
               <template #title>
                  Total interest
               </template>
               <template #value>
                  {{ formatLKR(totalInterestAmount) }}
               </template>
            </StatItem>
            <StatItem>
               <template #title>
                  Total payable
               </template>
               <template #value>
                  {{ formatLKR(totalInterestAmount + pawn.amount) }}
               </template>
            </StatItem>
         </StatsGroup>
      </div>

      <!-- region: interest breakdown table -->
      <div class="flex justify-center overflow-auto">
         <div class="rounded-2xl border max-h-96 overflow-auto">
            <UTable
               class=""
               :rows="monthlyBreakdown"
            >
               <!-- region: Headers -->
               <template #month-header="{ column }">
                  <p class="text-end">
                     {{ column.label }}
                  </p>
               </template>
               <template #days-header="{ column }">
                  <p class="text-end">
                     {{ column.label }}
                  </p>
               </template>
               <template #interestRate-header="{ column }">
                  <p class="text-end">
                     {{ column.label }}
                  </p>
               </template>
               <template #interestAmount-header="{ column }">
                  <p class="text-end">
                     {{ column.label }}
                  </p>
               </template>
               <!-- endregion: Headers -->

               <!-- region: Data -->
               <template #interestRate-data="{ row }">
                  <p class="text-end">
                     {{ row.interestRate }} %
                  </p>
               </template>

               <template #interestAmount-data="{ row }">
                  <p class="text-end">
                     {{ formatLKR(row.interestAmount) }}
                  </p>
               </template>
               <!-- endregion: Data -->
            </UTable>
         </div>
      </div>
      <!-- endregion: interest breakdown table -->

      <!-- region: interest rates -->
      <div class="mt-5 flex justify-center">
         <StatsGroup>
            <StatItem size="sm">
               <template #title>
                  First week
               </template>
               <template #value>
                  {{ interestRates.firstWeek * 100 }} %
               </template>
            </StatItem>

            <StatItem size="sm">
               <template #title>
                  Second week
               </template>
               <template #value>
                  {{ interestRates.secondWeek * 100 }} %
               </template>
            </StatItem>

            <StatItem size="sm">
               <template #title>
                  Third week
               </template>
               <template #value>
                  {{ interestRates.thirdWeek * 100 }} %
               </template>
            </StatItem>

            <StatItem size="sm">
               <template #title>
                  Fourth week
               </template>
               <template #value>
                  {{ interestRates.fourthWeek * 100 }} %
               </template>
            </StatItem>
         </StatsGroup>
      </div>
      <!-- endregion: interest rates -->
   </div>
</template>

<style scoped lang="postcss"></style>
