<script setup lang="ts">
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

const { pawn, label = 'Enable' } = defineProps<{
   pawn: Pawn;
   label?: string;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

async function handleReActivatePawn() {
   try {
      await useNuxtApp().$api('/pawn/update/state', {
         method: 'PATCH',
         body: {
            pawn_id: pawn.id,
            state: 'ACTIVE',
         },
      });

      emit('update');
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton color="orange" variant="soft" icon="i-uil:check" @click="handleReActivatePawn()">
      {{ label }}
   </UButton>
</template>

<style scoped lang="postcss">

</style>
