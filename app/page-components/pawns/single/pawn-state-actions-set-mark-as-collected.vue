<script setup lang="ts">
import { z } from 'zod';
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const { setPawn, getInterest } = usePawnInterestCalc();

const totalAmountPayable = ref(0);

const isOpenMarkAsCollectedModal = ref(false);

const formSetAsCollectedSchema = z.object({
   collected_date: z.string().date(),
   collected_amount: z.coerce
      .number({ message: 'Needs to be a valid amount' })
      .positive('Needs to be a valid amount')
      .min(pawn.amount, 'Should be greater than pawned amount'),
});

const formSetAsCollectedState = reactive({
   pawn_id: pawn.id,
   state: 'COLLECTED',
   collected_date: todayAsString(),
   collected_amount: 0,
});

/**
 * Handle set as collected
 */
async function handleMarkAsCollected() {
   try {
      await useNuxtApp().$api('/pawn/update/state', {
         method: 'PATCH',
         body: {
            ...formSetAsCollectedState,
         },
      });

      emit('update');
      isOpenMarkAsCollectedModal.value = false;
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */

watchImmediate(
   () => pawn,
   (pawn) => {
      setPawn(pawn);
   },
);

watch(isOpenMarkAsCollectedModal, () => {
   totalAmountPayable.value = Number((getInterest().totalInterestAmount + pawn.amount).toFixed(2));
   formSetAsCollectedState.collected_amount = totalAmountPayable.value;
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton
      v-if="pawn.state === 'ACTIVE'"
      @click="isOpenMarkAsCollectedModal = true"
   >
      Mark as collected
   </UButton>

   <!-- region: Modal -->
   <UModal
      v-model="isOpenMarkAsCollectedModal"
      prevent-close
   >
      <div class="flex flex-col">
         <header class="px-5 pt-5">
            <Heading3>Mark as collected</Heading3>
         </header>
         <UDivider />

         <UForm
            :schema="formSetAsCollectedSchema"
            :state="formSetAsCollectedState"
            @submit="handleMarkAsCollected"
         >
            <section class="p-5">
               <div class="grid grid-cols-2 gap-5">
                  <UFormGroup
                     label="Collection date"
                     name="collected_date"
                  >
                     <InputDate
                        v-model="formSetAsCollectedState.collected_date"
                        :deny-past="!user?.isAdmin"
                     />
                  </UFormGroup>

                  <UFormGroup
                     label="Amount"
                     name="collected_amount"
                  >
                     <UInput v-model="formSetAsCollectedState.collected_amount" />
                  </UFormGroup>
               </div>
            </section>

            <UDivider />

            <footer class="flex justify-end gap-3 p-5">
               <UButton type="submit">
                  Update
               </UButton>
               <UButton
                  color="gray"
                  @click="isOpenMarkAsCollectedModal = false"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
      </div>
   </UModal>
   <!-- endregion: Modal -->
</template>

<style scoped lang="postcss">

</style>
