<script setup lang="ts">
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

const emit = defineEmits(['delete']);

/* ---------------------------------------------------------------------------------------------- */

const isDeleteModalOpen = ref(false);
const isDeleting = ref(false);

/* ---------------------------------------------------------------------------------------------- */

async function handleDelete() {
   isDeleting.value = true;

   try {
      await useNuxtApp().$api('/pawn/delete', {
         method: 'DELETE',
         body: {
            id: pawn.id,
         },
      });

      emit('delete');
      isDeleteModalOpen.value = false;
   }
   catch (error) {
      console.error(error);
   }

   isDeleting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton
      color="red"
      icon="i-uil:trash-alt"
      @click="isDeleteModalOpen = true"
   >
      Delete
   </UButton>

   <UModal
      v-model="isDeleteModalOpen"
      prevent-close
   >
      <div class="flex flex-col">
         <header class="px-5 pt-5">
            <Heading3>Delete pawn</Heading3>
         </header>
         <UDivider />

         <section class="p-5">
            <p>Are you sure you want to delete this pawn?</p>
            <p class="mt-2 text-sm text-red-500">
               This action cannot be reversed.
            </p>

            <div class="mt-5 flex justify-end gap-3">
               <UButton
                  color="red"
                  :loading="isDeleting"
                  @click="handleDelete()"
               >
                  Delete
               </UButton>
               <UButton
                  color="gray"
                  @click="isDeleteModalOpen = false"
               >
                  Cancel
               </UButton>
            </div>
         </section>
      </div>
   </UModal>
</template>
