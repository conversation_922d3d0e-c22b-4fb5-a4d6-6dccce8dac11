<script setup lang="ts">
import { z } from 'zod';
import type { Pawn } from '~~/_backend/pawning/types';

/* ---------------------------------------------------------------------------------------------- */

const { pawn } = defineProps<{
   pawn: Pawn;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const isExtendModalOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   pledge_date: z.string().date('Needs to be a valid date'),
});

const formState = reactive({
   pawn_id: pawn.id,
   pledge_date: pawn.pledge_date,
});

async function handleExtendPawnPeriod() {
   try {
      await useNuxtApp().$api('/pawn/update/period', {
         method: 'PATCH',
         body: { ...formState },
      });

      emit('update');

      isExtendModalOpen.value = false;
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton
      color="gray"
      @click="isExtendModalOpen = true"
   >
      Extend pawn period
   </UButton>

   <UModal
      v-model="isExtendModalOpen"
      prevent-close
   >
      <div class="flex flex-col">
         <header class="px-5 pt-5">
            <Heading3>Extend pawning period</Heading3>
         </header>
         <UDivider />

         <UForm
            :state="formState"
            :schema="formSchema"
            @submit="handleExtendPawnPeriod()"
         >
            <section class="p-5">
               <div class="grid grid-cols-2 gap-5">
                  <UFormGroup
                     label="Pledge date"
                     name="pledge_date"
                  >
                     <InputDate v-model="formState.pledge_date" allow-future />
                  </UFormGroup>
               </div>
            </section>

            <UDivider />

            <footer class="flex justify-end gap-3 p-5">
               <UButton
                  type="submit"
               >
                  Update
               </UButton>
               <UButton
                  color="gray"
                  @click="isExtendModalOpen = false"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
      </div>
   </UModal>
</template>

<style scoped lang="postcss"></style>
