<script setup lang="ts">
import { z } from 'zod';
import { todayAsString } from '~/utils/datetime-utils';
import { useBillListingsStore } from '~/stores/bill-listings-store';
import { useJewelryListingsStore } from '~/stores/jewelries-listings-store';

/* ---------------------------------------------------------------------------------------------- */

const store = useJewelryListingsStore();
const { queryParams } = storeToRefs(store);

/* ---------------------------------------------------------------------------------------------- */

/**
 * Toolbar fields
 */

const stockId = ref<number | undefined>();

watchDebounced(
   stockId,
   (stockId) => {
      if (stockId) {
         queryParams.value.page = 1;
         queryParams.value.stock_id = stockId;
      }
   },
   { debounce: 500 },
);

/* ---------------------------------------------------------------------------------------------- */

const sortOrderOptions = [
   {
      id: 'asc',
      label: 'Ascending',
      icon: 'i-uil:sort-amount-up',
   },
   {
      id: 'desc',
      label: 'Descending',
      icon: 'i-uil:sort-amount-down',
   },
];

const locationOptions = [
   {
      id: 'ALL',
      label: 'All',
   },
   {
      id: 'STORE',
      label: 'Store',
   },
   {
      id: 'DISPLAY',
      label: 'Display',
   },
   {
      id: 'SOLD',
      label: 'Sold',
   },
];

const soldStatusOptions = [
   {
      id: 'ALL',
      label: 'All',
   },
   {
      id: 'YES',
      label: 'Yes',
   },
   {
      id: 'NO',
      label: 'No',
   },
];

/* ---------------------------------------------------------------------------------------------- */
/**
 * Watch query params
 */
const route = useRoute();

const RouteQuerySchema = z.object({
   query: z.coerce.string(),
   page: z.coerce.number(),
   karat: z.coerce.string(),
   location: z.coerce.string(),
   sold: z.coerce.string(),
   sort_by: z.coerce.string(),
   sort_order: z.coerce.string(),
   stock_id: z.union([z.number(), z.string()]),
   cost_min: z.coerce.number(),
   cost_max: z.coerce.number(),
   price_min: z.coerce.number(),
   price_max: z.coerce.number(),
   weight_min: z.coerce.number(),
   weight_max: z.coerce.number(),
});

const query = RouteQuerySchema.safeParse(route.query);
if (query.data) {
   queryParams.value = { ...(query.data as any) };
}

// watchDeep(
//    queryParams,
//    async (params) => {
//       await router.push({ query: params });
//    },
//    { immediate: true },
// );

/* ---------------------------------------------------------------------------------------------- */

/**
 * Advanced filters
 */

const showAdvancedFiltersModal = ref(false);

function resetFilters() {
   queryParams.value.cost_min = 0;
   queryParams.value.cost_max = 0;

   queryParams.value.price_min = 0;
   queryParams.value.price_max = 0;

   queryParams.value.weight_min = 0;
   queryParams.value.weight_max = 0;

   queryParams.value.karat = 'ALL';
   queryParams.value.location = 'ALL';
   queryParams.value.sold = 'ALL';
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <section class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex-1">
         <div class="flex flex-col gap-2 md:flex-row">
            <UInput
               v-model="stockId"
               placeholder="Stock #"
            />
         </div>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="flex gap-2">
         <USelectMenu
            v-model="queryParams.location"
            class="w-32"
            :options="locationOptions"
            value-attribute="id"
            option-attribute="label"
         />

         <USelectMenu
            v-model="queryParams.sort_order"
            class="w-32"
            :options="sortOrderOptions"
            value-attribute="id"
            option-attribute="label"
         />
         <UButton
            icon="i-uil:filter"
            square
            @click="showAdvancedFiltersModal = true"
         />
      </div>
      <!-- endregion: right -->
   </section>

   <UModal v-model="showAdvancedFiltersModal">
      <u-card
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <Heading3>Advanced filters</Heading3>
         </template>

         <section class="flex flex-col gap-5">
            <!-- region: Filters -->

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Location">
                  <USelectMenu
                     v-model="queryParams.location"
                     :options="locationOptions"
                     value-attribute="id"
                     option-attribute="label"
                  />
               </UFormGroup>

               <UFormGroup label="Sold Status">
                  <USelectMenu
                     v-model="queryParams.sold"
                     :options="soldStatusOptions"
                     value-attribute="id"
                     option-attribute="label"
                  />
               </UFormGroup>
            </div>

            <div>
               <UFormGroup label="Search">
                  <UInput v-model="queryParams.query" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Min cost">
                  <UInput v-model.lazy="queryParams.cost_min" />
               </UFormGroup>

               <UFormGroup label="Max cost">
                  <UInput v-model.lazy="queryParams.cost_max" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Min weight">
                  <UInput v-model.lazy="queryParams.weight_min" />
               </UFormGroup>

               <UFormGroup label="Max weight">
                  <UInput v-model.lazy="queryParams.weight_max" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Min price">
                  <UInput v-model.lazy="queryParams.price_min" />
               </UFormGroup>

               <UFormGroup label="Max price">
                  <UInput v-model.lazy="queryParams.price_max" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-2 gap-3">
               <UFormGroup label="Karat">
                  <SelectKarat v-model="queryParams.karat" />
               </UFormGroup>
            </div>

            <!-- endregion: Filters -->
         </section>

         <template #footer>
            <div class="flex justify-end gap-3">
               <UButton
                  icon="i-uil:check"
                  @click="showAdvancedFiltersModal = false"
               >
                  Apply & Close
               </UButton>

               <UButton
                  color="gray"
                  icon="i-uil:refresh"
                  @click="resetFilters()"
               >
                  Reset
               </UButton>
            </div>
         </template>
      </u-card>
   </UModal>
</template>

<style scoped lang="postcss"></style>
