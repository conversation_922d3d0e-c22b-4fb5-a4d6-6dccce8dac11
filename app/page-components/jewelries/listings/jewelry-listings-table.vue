<script setup lang="ts">
import type { Jewellery } from '~~/_backend/jewelries/types';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';
import type { BadgeColor } from '#ui/types';

/* ---------------------------------------------------------------------------------------------- */

const { items } = defineProps<{
   items: Jewellery[];
}>();

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const columns = computed(() => {
   const cols = [
      { key: 'id', label: 'Stock #' },
      { key: 'title', label: 'Title' },
      { key: 'category', label: 'Category' },
      { key: 'supplier', label: 'Supplier' },
      { key: 'weight', label: 'Weight' },
      { key: 'price', label: 'Price' },
      { key: 'karat', label: 'Kt' },
      { key: 'location', label: 'Location' },
      { key: 'sold', label: 'Sold' },
   ];

   // Only show cost column if user is admin
   if (user.value?.isAdmin) {
      cols.splice(5, 0, { key: 'cost', label: 'Cost' });
   }

   return cols;
});

interface Row {
   id: number | string;
   title: string;
   category: string;
   supplier: string;
   weight: number;
   cost: number;
   karat: string;
   price: number;
   location: string;
   sold: string;
}

const rows = ref<Row[]>([]);

watchDeep(
   () => items,
   (items) => {
      rows.value = [];

      items.forEach((item) => {
         const category = item.category?.title ?? '';
         const supplier = item.supplier?.supplier_name ?? '';

         rows.value.push({
            id: item.id,
            title: item.title,
            category,
            supplier,
            weight: item.weight,
            cost: item.cost,
            karat: item.karat,
            price: item.price,
            location: item.location,
            sold: item.sold,
         });
      });
   },
   {
      immediate: true,
   },
);

/* ---------------------------------------------------------------------------------------------- */

const locationColors = new Map<string, BadgeColor>([
   ['DISPLAY', 'green'],
   ['STORE', 'orange'],
   ['SOLD', 'red'],
]);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div class="rounded-2xl border mb-5">
      <UTable
         :columns
         :rows
      >
         <!-- region: header -->
         <template #weight-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <template #cost-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <template #price-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <template #location-header="{ column }">
            <div class="text-center">
               {{ column.label }}
            </div>
         </template>

         <template #sold-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>
         <!-- endregion: header -->

         <!-- region: data -->

         <template #id-data="{ row }">
            <div>
               <UButton
                  class="px-2"
                  size="sm"
                  :padded="false"
                  :to="`/jewelries/${row.id}`"
               >
                  {{ row.id }}
               </UButton>
            </div>
         </template>

         <template #weight-data="{ row }">
            <div class="text-right">
               {{ formatWeight(row.weight) }}
            </div>
         </template>

         <template #cost-data="{ row }">
            <div class="text-right">
               {{ formatLKR(row.cost) }}
            </div>
         </template>

         <template #price-data="{ row }">
            <div class="text-right">
               {{ formatLKR(row.price) }}
            </div>
         </template>

         <template #location-data="{ row }">
            <div class="text-center">
               <UBadge
                  size="xs"
                  variant="soft"
                  :color="locationColors.get(row.location) ?? 'green'"
               >
                  {{ row.location }}
               </UBadge>
            </div>
         </template>

         <template #sold-data="{ row }">
            <div class="text-right">
               <UBadge
                  size="xs"
                  variant="soft"
                  :color="row.sold === 'YES' ? 'blue' : 'amber'"
               >
                  {{ row.sold }}
               </UBadge>
            </div>
         </template>
         <!-- endregion: data -->
      </UTable>
   </div>
</template>

<style scoped lang="postcss"></style>
