<script setup lang="ts">
import { formatLKR, formatWeight } from '~/utils/formatting-utils';

const { items, price, cost, weight } = defineProps<{
   items?: number;
   cost?: number;
   price?: number;
   weight?: number;
}>();
</script>

<template>
   <StatsGroup>
      <StatItem size="sm">
         <template #title>
            Total
         </template>
         <template #value>
            {{ items }}
         </template>
      </StatItem>

      <StatItem size="sm">
         <template #title>
            Cost
         </template>
         <template #value>
            {{ formatLKR(cost ?? 0) }}
         </template>
      </StatItem>

      <StatItem size="sm">
         <template #title>
            Price
         </template>
         <template #value>
            {{ formatLKR(price ?? 0) }}
         </template>
      </StatItem>

      <StatItem size="sm">
         <template #title>
            Weight
         </template>
         <template #value>
            {{ formatWeight(weight ?? 0) }}
         </template>
      </StatItem>
   </StatsGroup>
</template>

<style scoped lang="postcss"></style>
