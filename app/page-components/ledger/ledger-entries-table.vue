<script setup lang="ts">
import dayjs from 'dayjs';
import type { LedgerEntry } from '~~/_backend/ledger/types';
import EditLedgerEntry from '~/page-components/ledger/edit-ledger-entry.vue';
import { DeleteLedgerEntry } from '#components';

/* ---------------------------------------------------------------------------------------------- */

const { entries } = defineProps<{
   entries: LedgerEntry[];
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'entry_date', label: 'Date' },
   { key: 'title', label: 'Title' },
   { key: 'weight', label: 'Weight' },
   { key: 'debit', label: 'Debit' },
   { key: 'credit', label: 'Credit' },
   { key: 'actions', label: '' },
];

interface Row {
   id: number | string;
   entry_date: string;
   title: string;
   weight: string;
   debit: string;
   credit: string;
   isOpenOrClose: boolean;
   class: string;
}

const rows = ref<Row[]>([]);

watchDeep(
   () => entries,
   (entries) => {
      rows.value = [];

      entries.forEach((entry) => {
         let classNames = '';
         let debit = '';
         let credit = '';
         let isOpenOrClose = false;

         if (entry.entry_type === 'CREDIT') {
            classNames = 'bg-red-50/50';
            credit = formatLKR(entry.amount);
         }
         else if (entry.entry_type === 'DEBIT') {
            classNames = 'bg-green-50/50';
            debit = formatLKR(entry.amount);
         }
         else if (entry.entry_type === 'OPENING') {
            isOpenOrClose = true;
            if (entry.amount > 0) {
               debit = formatLKR(entry.amount);
            }
            else {
               credit = formatLKR(entry.amount);
            }
         }
         else if (entry.entry_type === 'CLOSING') {
            isOpenOrClose = true;
            if (entry.amount > 0) {
               debit = formatLKR(entry.amount);
            }
            else {
               credit = formatLKR(entry.amount);
            }
         }

         rows.value.push({
            id: entry.id,
            entry_date: entry.entry_date,
            title: entry.title,
            class: classNames,
            debit,
            credit,
            isOpenOrClose,
            weight: entry.weight ? formatWeight(entry.weight) : '',
         });
      });
   },
);

/* ---------------------------------------------------------------------------------------------- */

const modal = useModal();

function showEditLedgerEntryModal(entryId: string | number) {
   const entry = entries.find(entry => entry.id === entryId);

   if (entry) {
      modal.open(EditLedgerEntry, {
         entry,
         onUpdate() {
            emit('update');
         },
      });
   }
}

/* ---------------------------------------------------------------------------------------------- */

function showDeleteLedgerEntryModal(entryId: string | number) {
   const entry = entries.find(entry => entry.id === entryId);

   if (entry) {
      modal.open(DeleteLedgerEntry, {
         entry,
         onDelete() {
            emit('update');
         },
      });
   }
}

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

function canEdit(entryDate: string) {
   if (user.value?.isAdmin) {
      return true;
   }
   else {
      return dayjs().diff(dayjs(entryDate), 'days') <= 1;
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div class="rounded-xl border">
      <UTable
         :columns
         :rows
      >
         <!-- region: headers -->
         <template #debit-header="{ column }">
            <div class="text-end">
               {{ column.label }}
            </div>
         </template>

         <template #credit-header="{ column }">
            <div class="text-end">
               {{ column.label }}
            </div>
         </template>
         <!-- endregion: headers -->

         <!-- region: data -->
         <template #debit-data="{ row }">
            <div class="text-end font-bold text-green-600">
               {{ row.debit }}
            </div>
         </template>

         <template #credit-data="{ row }">
            <div class="text-end font-bold text-red-600">
               {{ row.credit }}
            </div>
         </template>

         <template #actions-data="{ row }">
            <div
               v-if="!row.isOpenOrClose"
               class="flex gap-1 text-center"
            >
               <template v-if="canEdit(row.entry_date)">
                  <UButton
                     size="xs"
                     icon="i-uil:edit-alt"
                     square
                     color="gray"
                     @click="showEditLedgerEntryModal(row.id)"
                  ></UButton>
                  <UButton
                     size="xs"
                     icon="i-uil:trash-alt"
                     square
                     color="red"
                     @click="showDeleteLedgerEntryModal(row.id)"
                  ></UButton>
               </template>
            </div>
         </template>

         <!-- endregion: data -->
      </UTable>
   </div>
</template>

<style scoped lang="postcss"></style>
