<script setup lang="ts">
import { z } from 'zod';
import InputCreditDebitToggle from '~/page-components/ledger/input-credit-debit-toggle.vue';
import type { LedgerEntry } from '~~/_backend/ledger/types';

const { entry } = defineProps<{
   entry: LedgerEntry;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const modal = useModal();

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   entry_date: z.string().date(),
   title: z.string().min(1, 'Required'),
   entry_type: z.enum(['CREDIT', 'DEBIT']),
   amount: z.coerce.number().positive('Should be a valid amount'),
   weight: z.union([
      z.literal(''),
      z.coerce.number().positive('Should be a valid amount'),
      z.undefined(),
   ]),
});

type FormState = z.infer<typeof formSchema>;

const formState = reactive({
   id: entry.id,
   title: entry.title,
   entry_date: entry.entry_date,
   entry_type: entry.entry_type,
   amount: Math.abs(entry.amount),
   weight: entry.weight ?? '',
} as FormState);

async function handleSubmit() {
   if (formState.weight === '') {
      formState.weight = undefined;
   }

   try {
      await useNuxtApp().$api('/ledger-entry/update', {
         method: 'PATCH',
         body: formState,
      });

      emit('update');
      await modal.close();
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal
      prevent-close
      :ui="{
         width: 'sm:max-w-screen-sm',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <Heading3>Update ledger entry</Heading3>
         </template>

         <!-- region: form -->
         <section>
            <UForm
               class="flex flex-col gap-5"
               :schema="formSchema"
               :state="formState"
               @submit="handleSubmit()"
            >
               <div class="grid grid-cols-2 gap-3">
                  <UFormGroup
                     label="Entry date"
                     name="entry_date"
                  >
                     <InputDate
                        v-model="formState.entry_date"
                        :readonly="!user?.isAdmin"
                     />
                  </UFormGroup>
               </div>

               <div class="grid grid-cols-1">
                  <UFormGroup
                     label="Title"
                     name="title"
                  >
                     <UInput v-model="formState.title" />
                  </UFormGroup>
               </div>

               <div class="grid grid-cols-1 gap-3">
                  <InputCreditDebitToggle v-model="formState.entry_type" />
               </div>

               <div class="grid grid-cols-2 gap-3">
                  <UFormGroup label="Amount" name="amount">
                     <UInput v-model="formState.amount" />
                  </UFormGroup>

                  <UFormGroup label="Weight" name="weight" help="Leave it empty to avoid">
                     <UInput v-model="formState.weight" />
                  </UFormGroup>
               </div>

               <footer class="flex w-full justify-end gap-3">
                  <UButton
                     type="submit"
                     icon="i-uil:save"
                  >
                     Save
                  </UButton>
                  <UButton
                     color="gray"
                     icon="i-uil:times"
                     @click="modal.close()"
                  >
                     Cancel
                  </UButton>
               </footer>
            </UForm>
         </section>
         <!-- endregion: form -->
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
