<script setup lang="ts">
const modelValue = defineModel<'CREDIT' | 'DEBIT'>({ default: 'DEBIT' });

function selectItem(value: 'CREDIT' | 'DEBIT') {
   modelValue.value = value;
}
</script>

<template>
   <div class="grid grid-cols-2 gap-3">
      <div
         class="flex cursor-pointer items-center justify-center px-3 py-2 font-bold uppercase rounded-lg"
         :class="modelValue === 'CREDIT' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-300'"
         @click="selectItem('CREDIT')"
      >
         Credit
      </div>
      <div
         class="flex cursor-pointer items-center justify-center px-3 py-2 font-bold uppercase rounded-lg"
         :class="modelValue === 'DEBIT' ? 'bg-green-500 text-white' : 'bg-gray-100 text-gray-300'"
         @click="selectItem('DEBIT')"
      >
         Debit
      </div>
   </div>
</template>

<style scoped lang="postcss"></style>
