<script setup lang="ts">
import type { LedgerEntry } from '~~/_backend/ledger/types';

/* ---------------------------------------------------------------------------------------------- */

const { entry } = defineProps<{
   entry: LedgerEntry;
}>();

const emit = defineEmits(['delete']);

/* ---------------------------------------------------------------------------------------------- */

const modal = useModal();

/* ---------------------------------------------------------------------------------------------- */

/**
 * Handles delete action
 */
async function handleDelete() {
   try {
      await useNuxtApp().$api('/ledger-entry/delete', {
         method: 'DELETE',
         body: {
            id: entry.id,
         },
      });

      emit('delete');
      await modal.close();
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UModal prevent-close>
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <Heading3>Delete ledger entry</Heading3>
         </template>

         <section>
            <p class="">
               Are you sure to delete the following entry?
            </p>

            <div class="my-2">
               <UAlert
                  color="primary"
                  variant="soft"
                  :title="entry.entry_date"
                  :description="entry.title"
               ></UAlert>
            </div>

            <p class="text-center text-sm text-red-500">
               This action cannot be reversed.
            </p>
         </section>

         <template #footer>
            <div class="flex justify-end gap-3">
               <UButton color="red" @click="handleDelete()">
                  yes, confirm delete
               </UButton>
               <UButton
                  color="gray"
                  @click="modal.close()"
               >
                  Cancel
               </UButton>
            </div>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
