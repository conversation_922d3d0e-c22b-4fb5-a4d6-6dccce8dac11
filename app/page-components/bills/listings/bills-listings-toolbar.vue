<script setup lang="ts">
import { todayAsString } from '~/utils/datetime-utils';
import { useBillListingsStore } from '~/stores/bill-listings-store';

/* ---------------------------------------------------------------------------------------------- */

const billListingsStore = useBillListingsStore();
const { queryParams } = storeToRefs(billListingsStore);

/* ---------------------------------------------------------------------------------------------- */
/*
 * Toolbar fields
 */

const billId = ref<number | undefined>();
const dateRange = ref([todayAsString(), todayAsString()]);

watchDeep(dateRange, ([start, end]) => {
   if (start && end) {
      queryParams.value.date_min = start;
      queryParams.value.date_max = end;
   }
});

watch(billId, (billId) => {
   if (billId)
      queryParams.value.bill_id = billId;
});

/* ---------------------------------------------------------------------------------------------- */

const sortOrder = [
   {
      id: 'asc',
      label: 'Ascending',
   },
   {
      id: 'desc',
      label: 'Descending',
   },
];

const state = [
   {
      id: 'ALL',
      label: 'All',
   },
   {
      id: 'OPEN',
      label: 'Open',
   },
   {
      id: 'CLOSE',
      label: 'Close',
   },
];

const dropdownState = ref('ALL');

watch(dropdownState, (state: any) => {
   if (state !== 'ALL') {
      queryParams.value.state = state;
   }
   else {
      queryParams.value.state = null;
   }
});

/* ---------------------------------------------------------------------------------------------- */

const router = useRouter();
const route = useRoute();

/**
 * Watch the query params and update the URL query
 */
watch(
   queryParams,
   async (params) => {
      /*
       * Update the URL query
       */
      await router.push({
         query: params,
      });

      /*
       * Updates the query params from the URL query
       * This cyclic approach makes sure that the UI element values are updated
       * according to the latest query values
       */
      const { date_min, date_max, sort_order } = route.query;

      if (date_max && date_min && sort_order) {
         queryParams.value.date_max = date_max.toString();
         queryParams.value.date_min = date_min.toString();
         queryParams.value.sort_order = sort_order.toString() as 'asc' | 'desc';

         dateRange.value = [date_min.toString(), date_max.toString()];
      }
   },
   { immediate: true, deep: true },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <section class="flex w-full flex-col gap-5 rounded-2xl bg-primaryColor-50 p-2 md:flex-row">
      <!-- region: left -->
      <div class="flex-1">
         <div class="flex flex-col gap-2 md:flex-row">
            <UInput
               v-model="billId"
               placeholder="Bill #"
            />
            <UButton>Find</UButton>
            <InputDateRange
               v-model="dateRange"
               class="lg:max-w-64"
            />
         </div>
      </div>
      <!-- endregion: left -->

      <!-- region: right -->
      <div class="flex gap-2">
         <USelectMenu
            v-model="queryParams.sort_order"
            :options="sortOrder"
            value-attribute="id"
            option-attribute="label"
         />
         <USelectMenu
            v-model="dropdownState"
            class="w-24"
            :options="state"
            value-attribute="id"
            option-attribute="label"
         />
         <UButton @click="billListingsStore.fetch()">
            Search
         </UButton>
      </div>
      <!-- endregion: right -->
   </section>
</template>

<style scoped lang="postcss"></style>
