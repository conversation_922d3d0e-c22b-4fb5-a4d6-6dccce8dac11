<script setup lang="ts">
import dayjs from 'dayjs';
import { toDateString } from '~/utils/datetime-utils';
import { formatLKR } from '~/utils/formatting-utils';
import type { Bill } from '~~/_backend/bills/types';

/* ---------------------------------------------------------------------------------------------- */

const props = defineProps<{
   items: Bill[];
}>();

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'id', label: 'ID' },
   { key: 'date', label: 'Date' },
   { key: 'customer', label: 'Customer' },
   { key: 'description', label: 'Description' },
   { key: 'by', label: 'By' },
   { key: 'amount', label: 'Cost' },
   { key: 'total', label: 'Total Billed' },
   { key: 'status', label: 'Status' },
];

/* ---------------------------------------------------------------------------------------------- */

interface Row {
   id: number | string;
   date: string;
   customer: string;
   by: string;
   amount: number;
   total: number;
   description: string;
   status: string;
}

const rows = ref<Row[]>([]);

watchDeep(
   () => props,
   (props) => {
      rows.value = [];

      props.items.forEach((item) => {
         rows.value.push({
            id: item.id,
            date: toDateString(dayjs(item.bill_date)),
            customer: item.customer.customer_name,
            by: item.billed_by,
            amount: item.bill_items_cost,
            total: item.bill_items_total,
            description: item.description,
            status: item.state,
         });
      });
   },
);

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <div>
      <UTable
         :columns="columns"
         :rows="rows"
      >
         <!-- region: Headers -->
         <template #amount-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <template #total-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <template #status-header="{ column }">
            <div class="text-right">
               {{ column.label }}
            </div>
         </template>

         <!-- endregion: Headers -->

         <!-- region: Rows -->

         <template #id-data="{ row }">
            <div>
               <UButton
                  class="px-2"
                  size="sm"
                  :padded="false"
                  :to="`/bills/${row.id}`"
               >
                  {{ row.id }}
               </UButton>
            </div>
         </template>

         <template #amount-data="{ row }">
            <div class="text-right">
               <template v-if="user?.isAdmin">
                  {{ formatLKR(row.amount) }}
               </template>
               <template v-else>
                  -
               </template>
            </div>
         </template>

         <template #total-data="{ row }">
            <div class="text-right">
               {{ formatLKR(row.total) }}
            </div>
         </template>

         <template #status-data="{ row }">
            <div class="text-right">
               <UBadge
                  size="xs"
                  variant="soft"
                  :color="row.status === 'OPEN' ? 'red' : 'green'"
               >
                  {{ row.status }}
               </UBadge>
            </div>
         </template>
         <!-- endregion: Rows -->
      </UTable>
   </div>
</template>

<style scoped lang="postcss"></style>
