<script setup lang="ts">
import dayjs from 'dayjs';
import { type BillsStats, BillsStatsSchema } from '~~/_backend/bills/types';
import StatsGroup from '~/components/stats/stats-group.vue';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';
import { toDateString } from '~/utils/datetime-utils';

/* ---------------------------------------------------------------------------------------------- */

const params = {
   date_min: toDateString(dayjs()),
   date_max: toDateString(dayjs()),
};

const { data: billStatsData } = useApiFetch<any>('/bills/stats', {
   query: {
      ...params,
   },
});

const billsStats = computed<BillsStats | null>(() => {
   if (billStatsData.value) {
      const data = BillsStatsSchema.safeParse(billStatsData.value);
      return data.data ? data.data : null;
   }
   return null;
});
</script>

<template>
   <!-- region: Billing stats -->
   <section
      v-if="billsStats"
      class=""
   >
      <header>
         <Heading1>
            Today's billing stats ({{ toDateString(dayjs()) }})
         </Heading1>
      </header>

      <div class="flex flex-col gap-5">
         <StatsGroup>
            <StatItem>
               <template #title>
                  Total bills
               </template>
               <template #value>
                  {{ billsStats.count }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Gold sold
               </template>
               <template #value>
                  {{ formatWeight(billsStats.total_weight) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Sales
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_sale) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Discount
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_discount) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Tax
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_tax) }}
               </template>
            </StatItem>
         </StatsGroup>

         <StatsGroup>
            <StatItem>
               <template #title>
                  Total cost
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_cost) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Total price
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_price) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Profit
               </template>
               <template #value>
                  {{
                     formatLKR(
                        billsStats.total_price - billsStats.total_cost - billsStats.total_discount,
                     )
                  }}
               </template>
            </StatItem>
         </StatsGroup>
      </div>
   </section>
   <!-- endregion: Billing stats -->
</template>

<style scoped lang="postcss"></style>
