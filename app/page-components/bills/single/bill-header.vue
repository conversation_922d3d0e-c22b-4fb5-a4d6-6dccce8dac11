<script setup lang="ts">
import { startCase } from 'es-toolkit/string';
import type { Bill } from '~~/_backend/bills/types';
import { formatDateString } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

const props = defineProps<{
   bill: Bill;
}>();
</script>

<template>
   <div
      class="p-4  rounded-xl mb-10" :class="[
         bill.state === 'OPEN' ? 'bg-red-50' : 'bg-primaryColor-50',
      ]"
   >
      <div class="flex flex-col gap-5 md:flex-row md:gap-3 justify-between">
         <div>
            <h1 class="text-4xl font-bold">
               Bill # {{ bill.id }}
            </h1>
            <h3 class="text-2xl font-bold text-secondaryColor-500">
               {{ formatDateString(bill.bill_date) }}
            </h3>
         </div>

         <div class="text-end">
            <h1 class="text-4xl font-bold">
               {{ startCase(bill.customer.customer_name) }}
            </h1>
            <h3 class="text-secondaryColor-500">
               {{ bill.customer.address }}
            </h3>
         </div>
      </div>
   </div>
</template>

<style scoped lang="postcss">

</style>
