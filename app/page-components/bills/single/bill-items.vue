<script setup lang="ts">
import type { Bill, BillItem } from '~~/_backend/bills/types';
import { formatDateString, formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

const props = defineProps<{
   items: BillItem[];
   billState: 'CLOSE' | 'OPEN';
   readonly?: boolean;
}>();

const emit = defineEmits(['delete']);

/* ---------------------------------------------------------------------------------------------- */

const { user } = useUserSession();

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'stock_id', label: 'Stock#' },
   { key: 'title', label: 'Title' },
   { key: 'karat', label: 'Kt' },
   { key: 'weight', label: 'Weight' },
   { key: 'cost', label: 'Cost' },
   { key: 'price', label: 'Price' },
   { key: 'options', label: 'Options' },
];

const rows = ref<{ [key: string]: any }[]>([]);

watchImmediate(
   () => props.items,
   (items) => {
      rows.value = [];

      items.forEach((item) => {
         rows.value.push({
            id: item.id,
            stock_id: item.jewelry.stock_id,
            title: item.jewelry.title,
            price: formatLKR(item.jewelry.price),
            cost: formatLKR(item.jewelry.cost),
            karat: item.jewelry.karat,
            weight: formatWeight(item.jewelry.weight),
         });
      });
   },
   {
      deep: true,
   },
);

/* ---------------------------------------------------------------------------------------------- */

const isDeleting = ref(false);
const deleteErrors = ref('');

async function handleDeleteItem(itemId: number) {
   isDeleting.value = true;

   try {
      await useNuxtApp().$api<{ id: string | number }>('/bill/remove-item', {
         method: 'POST',
         body: {
            id: itemId,
         },
      });

      emit('delete');
   }
   catch (error) {
      console.error(error);
      deleteErrors.value = 'Failed to delete. Try again';
   }

   isDeleting.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UTable
      :columns
      :rows
   >
      <!-- region: Headers -->
      <template #id-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #price-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #cost-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #karat-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #weight-header="{ column }">
         <div class="text-right">
            {{ column.label }}
         </div>
      </template>

      <template #options-header="{ column }">
         <div class="text-center">
            {{ column.label }}
         </div>
      </template>

      <!-- endregion: Headers -->

      <!-- region: Rows -->

      <template #id-data="{ row }">
         <div class="text-right">
            {{ row.id }}
         </div>
      </template>

      <template #cost-data="{ row }">
         <div v-if="user?.isAdmin" class="text-right">
            {{ row.cost }}
         </div>
         <div v-else class="text-right">
            -
         </div>
      </template>

      <template #price-data="{ row }">
         <div class="text-right">
            {{ row.price }}
         </div>
      </template>

      <template #karat-data="{ row }">
         <div class="text-right">
            {{ row.karat }} kt
         </div>
      </template>

      <template #weight-data="{ row }">
         <div class="text-right">
            {{ row.weight }}
         </div>
      </template>

      <template #options-data="{ row }">
         <template v-if="!readonly">
            <div
               v-if="billState === 'OPEN'"
               class="flex items-center justify-center gap-2"
            >
               <UButton
                  color="red"
                  icon="i-fa6-solid:trash-can"
                  @click="handleDeleteItem(row.id)"
               />
            </div>
            <div v-else>
               <p class="text-center text-xs">
                  Open the bill to edit
               </p>
            </div>
         </template>
      </template>
      <!-- endregion: Rows -->
   </UTable>
</template>

<style scoped lang="postcss"></style>
