<script setup lang="ts">
import { z } from 'zod';
import type { Bill } from '~~/_backend/bills/types';

/* ---------------------------------------------------------------------------------------------- */

const { bill } = defineProps<{
   bill: Bill;
}>();

const emit = defineEmits(['update']);

const isModalOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const formSchema = z.object({
   discount: z.coerce.number().nonnegative('Invalid amount'),
   tax: z.coerce.number().nonnegative('Invalid amount'),
});

const formState = reactive({
   discount: bill.discount as string | number,
   tax: bill.tax as string | number,
});

/**
 * Handle submit
 */
async function handleSubmit() {
   if (formState.tax === '')
      formState.tax = 0;

   if (formState.discount === '')
      formState.discount = 0;

   try {
      await useNuxtApp().$api('/bill/update', {
         method: 'PATCH',
         body: {
            id: bill.id,
            discount: formState.discount,
            tax: formState.tax,
         },
      });

      emit('update');
      isModalOpen.value = false;
   }
   catch (error) {
      console.error(error);
   }
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton @click="isModalOpen = true">
      Add discount & tax
   </UButton>

   <UModal
      v-model="isModalOpen"
      prevent-close
   >
      <UCard>
         <template #header>
            <Heading3>Add discount / tax</Heading3>
         </template>

         <!-- region: form -->
         <UForm
            class="flex flex-col gap-5"
            :state="formState"
            :schema="formSchema"
            @submit="handleSubmit()"
         >
            <div class="grid grid-cols-1">
               <UFormGroup
                  label="Discount / Old gold"
                  name="discount"
               >
                  <UInput v-model="formState.discount" />
               </UFormGroup>
            </div>

            <div class="grid grid-cols-1">
               <UFormGroup
                  label="Tax"
                  name="tax"
               >
                  <UInput v-model="formState.tax" />
               </UFormGroup>
            </div>

            <footer class="flex justify-end gap-3">
               <UButton type="submit">
                  Update
               </UButton>
               <UButton
                  color="gray"
                  @click="isModalOpen = false"
               >
                  Cancel
               </UButton>
            </footer>
         </UForm>
         <!-- endregion: form -->
      </UCard>
   </UModal>
</template>

<style scoped lang="postcss"></style>
