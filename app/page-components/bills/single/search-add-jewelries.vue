<script setup lang="ts">
import { useApiSearchJewelries } from '~~/_backend/jewelries/api';
import type { Jewellery } from '~~/_backend/jewelries/types';
import type { Bill } from '~~/_backend/bills/types';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';

/* ---------------------------------------------------------------------------------------------- */

const { bill } = defineProps<{
   bill: Bill;
}>();

const emit = defineEmits(['update']);

/* ---------------------------------------------------------------------------------------------- */

const isModalOpen = ref(false);

/* ---------------------------------------------------------------------------------------------- */

const query = ref('');
const debouncedQuery = refDebounced(query, 500);

const apiSearch = reactive(useApiSearchJewelries(debouncedQuery));

/* ---------------------------------------------------------------------------------------------- */

const columns = [
   { key: 'options', label: 'Select' },
   { key: 'stock_id', label: 'Stock #' },
   { key: 'title', label: 'Title' },
   { key: 'price', label: 'Price' },
   { key: 'weight', label: 'Weight' },
   { key: 'karat', label: 'Kt' },
];

const rows = ref<{ [key: string]: any }[]>([]);

watchImmediate(
   () => apiSearch.jewelries,
   (jewelries) => {
      rows.value = [];

      jewelries.forEach((jewelry) => {
         rows.value.push({
            id: jewelry.id,
            stock_id: jewelry.stock_id,
            title: jewelry.title,
            price: jewelry.price,
         });
      });
   },
);

/* ---------------------------------------------------------------------------------------------- */
/*
 * Select and add item to bill
 */

const selectedJewelry = ref<Jewellery | null>(null);
const billItemPrice = ref(0);

function selectJewelry(jewelryId: number | string) {
   selectedJewelry.value = apiSearch.jewelries.find(jewelry => jewelry.id === jewelryId) ?? null;
   if (selectedJewelry.value) {
      billItemPrice.value = selectedJewelry.value.price;
   }
}

const addingBillItemErrors = ref('');
const addingBillItem = ref(false);

/**
 * Add the jewelry to the bill
 */
async function handleAddJewelry() {
   if (!selectedJewelry.value)
      return;

   if (billItemPrice.value < selectedJewelry.value.price) {
      addingBillItemErrors.value = 'Item price cannot be less than mentioned price';
      return false;
   }

   addingBillItem.value = true;

   try {
      await useNuxtApp().$api<{ id: string | number }>('/bill/add-item', {
         method: 'POST',
         body: {
            bill_id: bill.id,
            jewelry_id: selectedJewelry.value?.id,
            price: billItemPrice.value,
         },
      });

      emit('update');

      selectedJewelry.value = null;

      isModalOpen.value = false;
   }
   catch (error) {
      console.error(error);
      addingBillItemErrors.value = 'Failed to add the item to this bill. Please try again';
   }

   addingBillItem.value = false;
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UButton
      icon="i-uil:plus"
      @click="isModalOpen = true"
   >
      Add item
   </UButton>

   <UModal
      v-model="isModalOpen"
      prevent-close
      :ui="{
         width: 'sm:max-w-screen-lg',
         padding: 'sm:p-4',
      }"
   >
      <UCard
         class=""
         :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
      >
         <template #header>
            <Heading3>Search for jewelry</Heading3>
         </template>

         <section>
            <UInput
               v-model="query"
               placeholder="Enter keywords to search..."
            ></UInput>

            <div class="h-4"></div>

            <div v-if="!selectedJewelry">
               <!-- region: Jewelry search results -->
               <UTable
                  :columns="columns"
                  :rows="rows"
               >
                  <template #options-header="">
                     <div class="w-8"></div>
                  </template>

                  <template #options-data="{ row }">
                     <UButton
                        variant="soft"
                        icon="i-uil:check"
                        square
                        @click="selectJewelry(row.id)"
                     ></UButton>
                  </template>
               </UTable>
               <!-- endregion: Jewelry search results -->
            </div>

            <div>
               <!-- region: add selected item to the bill -->
               <div
                  v-if="selectedJewelry"
                  class="flex flex-col items-center mb-5"
               >
                  <Heading3>
                     #{{ selectedJewelry.stock_id }} - {{ selectedJewelry.title }}
                     ({{ selectedJewelry.category?.title }})
                  </Heading3>

                  <div class="h-2"></div>

                  <div class="flex gap-3">
                     <StatsGroup>
                        <StatItem size="sm">
                           <template #title>
                              Kt
                           </template>
                           <template #value>
                              {{ selectedJewelry.karat }}
                           </template>
                        </StatItem>

                        <StatItem size="sm">
                           <template #title>
                              Weight
                           </template>
                           <template #value>
                              {{ formatWeight(selectedJewelry.weight) }}
                           </template>
                        </StatItem>

                        <StatItem size="sm">
                           <template #title>
                              Price
                           </template>
                           <template #value>
                              {{ formatLKR(selectedJewelry.price) }}
                           </template>
                        </StatItem>
                     </StatsGroup>
                  </div>

                  <div class="h-6"></div>

                  <div class="flex flex-col gap-5">
                     <UFormGroup
                        label="Item price to add"
                        size="xl"
                     >
                        <UInput v-model="billItemPrice" />
                     </UFormGroup>

                     <footer class="flex gap-3">
                        <UButton
                           class="flex-1"
                           icon="i-uil:plus"
                           :loading="addingBillItem"
                           @click="handleAddJewelry()"
                        >
                           Add item to bill
                        </UButton>
                        <UButton
                           color="gray"
                           @click="selectedJewelry = null"
                        >
                           Cancel
                        </UButton>
                     </footer>
                  </div>
               </div>

               <div v-if="addingBillItemErrors">
                  <UAlert color="red" variant="soft">
                     <template #description>
                        {{ addingBillItemErrors }}
                     </template>
                  </UAlert>
               </div>

               <!-- endregion: add selected item to the bill -->
            </div>
         </section>

         <template #footer>
            <div class="flex justify-end gap-3">
               <UButton
                  color="gray"
                  @click="isModalOpen = false"
               >
                  Close
               </UButton>
            </div>
         </template>
      </UCard>
   </UModal>
</template>

<style scoped>
/* Component styles go here */
</style>
