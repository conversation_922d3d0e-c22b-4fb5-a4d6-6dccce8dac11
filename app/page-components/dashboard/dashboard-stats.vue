<script setup lang="ts">
import StatsGroup from '~/components/stats/stats-group.vue';
import {
   type PawnStats,
   PawnsStatsSchema,
} from '~~/_backend/pawning/types';
import { formatLKR, formatNumber, formatWeight } from '~/utils/formatting-utils';
import { type BillsStats, BillsStatsSchema } from '~~/_backend/bills/types';

/* ---------------------------------------------------------------------------------------------- */

const { data: pawnStatsData } = useApiFetch<any>('/pawns/stats');

const pawningStats = computed<PawnStats | null>(() => {
   if (pawnStatsData.value) {
      return PawnsStatsSchema.parse(pawnStatsData.value);
   }

   return null;
});

/* ---------------------------------------------------------------------------------------------- */

const { data: billStatsData } = useApiFetch<any>('/bills/stats');

const billsStats = computed<BillsStats | null>(() => {
   if (billStatsData.value) {
      const data = BillsStatsSchema.safeParse(billStatsData.value);
      return data.data ? data.data : null;
   }
   return null;
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <!-- region: Pawning stats -->
   <section
      v-if="pawningStats"
      class="mb-10"
   >
      <header>
         <h1 class="mb-5 text-4xl font-bold text-secondaryColor-400">
            Pawning stats
         </h1>
      </header>

      <div class="flex">
         <StatsGroup>
            <StatItem>
               <template #title>
                  Total pawns
               </template>
               <template #value>
                  {{ formatNumber(pawningStats.count) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Active
               </template>
               <template #value>
                  {{ formatLKR(pawningStats.active_total) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Collected
               </template>
               <template #value>
                  {{ formatLKR(pawningStats.collected_total) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Expired
               </template>
               <template #value>
                  {{ formatLKR(pawningStats.expired_total) }}
               </template>
            </StatItem>
         </StatsGroup>
      </div>
   </section>
   <!-- endregion: Pawning stats -->

   <!-- region: Billing stats -->
   <section
      v-if="billsStats"
      class=""
   >
      <header>
         <h1 class="mb-5 text-4xl font-bold text-secondaryColor-400">
            Billing stats
         </h1>
      </header>

      <div class="flex">
         <StatsGroup>
            <StatItem>
               <template #title>
                  Total bills
               </template>
               <template #value>
                  {{ billsStats.count }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Weight
               </template>
               <template #value>
                  {{ formatWeight(billsStats.total_weight) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Sales
               </template>
               <template #value>
                  {{ formatLKR(billsStats.total_sale) }}
               </template>
            </StatItem>

            <StatItem>
               <template #title>
                  Profit
               </template>
               <template #value>
                  {{
                     formatLKR(
                        billsStats.total_price - billsStats.total_cost - billsStats.total_discount,
                     )
                  }}
               </template>
            </StatItem>
         </StatsGroup>
      </div>
   </section>
   <!-- endregion: Billing stats -->
</template>

<style scoped lang="postcss"></style>
