import { z } from 'zod';

/* ---------------------------------------------------------------------------------------------- */

export const PaginationLinkSchema = z.object({
   url: z.string().nullable(),
   label: z.string(),
   active: z.boolean(),
});

export const PaginatedItemsSchema = z.object({
   total: z.number(),
   current_page: z.number(),
   last_page: z.number(),

   from: z.number().nullable(),
   to: z.number().nullable(),
   per_page: z.number(),

   links: z.array(PaginationLinkSchema),
});

export type PaginationLink = z.infer<typeof PaginationLinkSchema>;
export type PaginatedItems = z.infer<typeof PaginatedItemsSchema>;
