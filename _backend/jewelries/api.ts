import type { Jewellery, PaginatedJewelries } from '~~/_backend/jewelries/types';
import {
   JewelryListSchema,
   JewelrySchema,
   PaginatedJewelriesSchema,
} from '~~/_backend/jewelries/types';

/* ------------------------------------------------------------------------------------------- */

export interface JewelrySortData {
   sort_by: 'id' | 'sold' | 'stock_id' | 'category_id' | 'supplier_id';
   sort_order: 'asc' | 'desc';
}

export interface FetchJewelriesQueryParams extends JewelrySortData {
   page: number;
   sold: 'YES' | 'NO' | 'ALL';
   location: 'DISPLAY' | 'STORE' | 'SOLD' | 'ALL';
   query: string;
   karat: 'ALL' | string;
   weight_min: number;
   weight_max: number;
   cost_min: number;
   cost_max: number;
   price_min: number;
   price_max: number;
   stock_id: string | number;
}

export interface FetchJewelryQueryParams {
   id: number | string;
}

/**
 * Fetch all jewelries, paginated
 */
export function useApiFetchJewelries(queryParams: Ref<FetchJewelriesQueryParams>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/jewelries', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<PaginatedJewelries | null>(() => {
      if (!error.value && data.value) {
         return PaginatedJewelriesSchema.parse(data.value);
      }
      return null;
   });

   const jewelries = computed<Jewellery[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      data,
      error,
      status,
      fetch,
      paginatedData,
      jewelries,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch single jewelry
 */
export function useApiFetchJewelry(queryParams: FetchJewelryQueryParams) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/jewelry', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const jewelry = computed<Jewellery | null>(() => {
      if (!error.value && data.value) {
         return JewelrySchema.parse(data.value);
      }
      return null;
   });

   return {
      fetch,
      error,
      status,
      jewelry,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Search jewelries
 */
export function useApiSearchJewelries(query: Ref<string>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/jewelries/search', {
      immediate: false,
      server: false,
      query: {
         query,
         sold: 'NO',
         location: 'DISPLAY',
      },
   });

   const jewelries = computed<Jewellery[]>(() => {
      if (!error.value && data.value) {
         return JewelryListSchema.parse(data.value);
      }
      return [];
   });

   return {
      error,
      fetch,
      status,
      jewelries,
   };
}
