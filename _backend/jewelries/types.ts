import { z } from 'zod';
import { PaginatedItemsSchema } from '~~/_backend/types';

/* ---------------------------------------------------------------------------------------------- */

/* ---------------------------------------------------------------------------------------------- */

export const JewelrySchema = z.object({
   id: z.union([z.string(), z.number()]),
   supplier_id: z.union([z.string(), z.number()]),
   category_id: z.union([z.string(), z.number()]),
   stock_id: z.union([z.string(), z.number()]),
   purchased_date: z.string(),
   title: z.string(),
   description: z.string(),
   weight: z.number(),
   cost: z.number(),
   price: z.number(),
   karat: z.string(),
   sold: z.enum(['YES', 'NO', 'ALL']),
   location: z.enum(['DISPLAY', 'STORE', 'SOLD', 'ALL']),

   supplier: z
      .object({
         id: z.union([z.string(), z.number()]),
         supplier_name: z.string(),
         address: z.string(),
         phone: z.string(),
         email: z.string(),
         mobile: z.string(),
      })
      .optional(),

   category: z
      .object({
         id: z.union([z.string(), z.number()]),
         title: z.string(),
         description: z.string(),
      })
      .optional(),

   billItem: z
      .object({
         id: z.union([z.string(), z.number()]),
         bill_id: z.union([z.string(), z.number()]),
         jewelry_id: z.union([z.string(), z.number()]),
      })
      .nullish(),
});

export const PaginatedJewelriesSchema = PaginatedItemsSchema.extend({
   data: z.array(JewelrySchema),
   total: z.number(),
   total_cost: z.number().optional(),
   total_price: z.number().optional(),
   total_weight: z.number().optional(),
});

export const JewelryListSchema = z.array(JewelrySchema);

/* ---------------------------------------------------------------------------------------------- */

export type Jewellery = z.infer<typeof JewelrySchema>;
export type PaginatedJewelries = z.infer<typeof PaginatedJewelriesSchema>;
