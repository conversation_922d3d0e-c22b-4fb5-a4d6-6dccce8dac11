import type {
   Category,
   FetchCategoryJewelriesParams,
   PaginatedCategories,
} from '~~/_backend/categories/types';
import {
   CategoryListSchema,
   CategorySchema,
   PaginatedCategoriesSchema,
} from '~~/_backend/categories/types';
import type {
   Jewellery,
   PaginatedJewelries,
} from '~~/_backend/jewelries/types';
import {
   PaginatedJewelriesSchema,
} from '~~/_backend/jewelries/types';
import type { Customer } from '~~/_backend/customers/types';
import { CustomerListSchema } from '~~/_backend/customers/types';

/* ---------------------------------------------------------------------------------------------- */

/**
 * Get all the categories
 */
export function useApiFetchCategories(queryParams: { page: number }) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/stock-categories', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedCategories = computed<PaginatedCategories | null>(() => {
      if (!error.value && data.value) {
         return PaginatedCategoriesSchema.parse(data.value);
      }
      return null;
   });

   return {
      data,
      paginatedCategories,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Get a category details
 */

export function useApiFetchCategory(categoryId: Ref<string | number>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/stock-category', {
      immediate: false,
      server: false,
      query: {
         id: categoryId.value,
      },
   });

   const category = computed<Category | null>(() => {
      if (!error.value && data.value) {
         return CategorySchema.parse(data.value);
      }
      return null;
   });

   return {
      data,
      category,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Get all the jewelries in a category
 */
export function useApiFetchCategoryJewelries(queryParams: FetchCategoryJewelriesParams) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/stock-category/jewelries', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<PaginatedJewelries | null>(() => {
      if (!error.value && data.value) {
         return PaginatedJewelriesSchema.parse(data.value);
      }
      return null;
   });

   const jewelries = computed<Jewellery[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      paginatedData,
      jewelries,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Search the categories
 */
export function useApiSearchCategories(query: Ref<string>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/stock-categories/search', {
      immediate: false,
      server: false,
      query: {
         query,
      },
   });

   const categories = computed<Category[]>(() => {
      if (!error.value && data.value) {
         return CategoryListSchema.parse(data.value);
      }
      return [];
   });

   return {
      categories,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */
