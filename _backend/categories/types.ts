import { z } from 'zod';
import { PaginatedItemsSchema } from '~~/_backend/types';

/* ---------------------------------------------------------------------------------------------- */

export interface FetchCategoryJewelriesParams {
   category_id: string | number;
   page: number;
   sold: 'YES' | 'NO' | 'ALL';
   location: 'DISPLAY' | 'STORE' | 'SOLD' | 'ALL';
   query: string;
   karat: 'ALL' | string;
   weight_min: number;
   weight_max: number;
}

/* ---------------------------------------------------------------------------------------------- */

export const CategorySchema = z.object({
   id: z.number(),
   title: z.string(),
   description: z.string(),
   jewelries_count: z.coerce.number().nullish(),
   jewelries_sum_cost: z.coerce.number().nullish(),
   jewelries_sum_weight: z.coerce.number().nullish(),
});

export const CategoryListSchema = z.array(CategorySchema);

export const PaginatedCategoriesSchema = PaginatedItemsSchema.extend({
   data: z.array(CategorySchema),
});

export type PaginatedCategories = z.infer<typeof PaginatedCategoriesSchema>;
export type Category = z.infer<typeof CategorySchema>;
