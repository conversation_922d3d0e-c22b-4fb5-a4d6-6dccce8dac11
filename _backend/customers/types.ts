import { z } from 'zod';
import { PaginatedItemsSchema } from '~~/_backend/types';

/* ---------------------------------------------------------------------------------------------- */

export const CustomerSchema = z.object({
   id: z.union([z.string(), z.number()]),
   customer_name: z.string(),
   address: z.string(),
   phone: z.string(),
   email: z.string(),
   nic: z.string(),
   since: z.string(),
   has_bills: z.boolean(),
   has_pawns: z.boolean(),
});

export const CustomerListSchema = z.array(CustomerSchema);
export const PaginatedCustomersSchema = PaginatedItemsSchema.extend({
   data: z.array(CustomerSchema),
});

export type Customer = z.infer<typeof CustomerSchema>;
export type PaginatedCustomers = z.infer<typeof PaginatedCustomersSchema>;
