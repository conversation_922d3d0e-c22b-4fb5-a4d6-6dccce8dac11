import type { Customer, PaginatedCustomers } from '~~/_backend/customers/types';
import {
   CustomerListSchema,
   CustomerSchema,
   PaginatedCustomersSchema,
} from '~~/_backend/customers/types';
import type { PaginatedPawns, Pawn } from '~~/_backend/pawning/types';
import { PaginatedPawnsSchema } from '~~/_backend/pawning/types';
import type { Bill, PaginatedBills } from '~~/_backend/bills/types';
import { PaginatedBillsSchema } from '~~/_backend/bills/types';
import type { PawningSortData } from '~~/_backend/pawning/api';

/**
 * Search customers using the query string
 */
export function useApiSearchCustomers(query: Ref<string>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/customers/search', {
      immediate: false,
      server: false,
      query: {
         query,
      },
   });

   const customers = computed<Customer[]>(() => {
      if (!error.value && data.value) {
         return CustomerListSchema.parse(data.value);
      }
      return [];
   });

   return {
      customers,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

export interface ApiFetchCustomersQueryParams {
   sort_by: 'id' | 'customer_name' | 'phone' | 'since';
   sort_order: 'asc' | 'desc';
   page: number;
   query: string;
}

export function useApiFetchCustomers(query: ApiFetchCustomersQueryParams) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/customers', {
      immediate: false,
      server: false,
      query,
   });

   const paginatedData = computed<PaginatedCustomers | null>(() => {
      if (!error.value && data.value) {
         return PaginatedCustomersSchema.parse(data.value);
      }
      return null;
   });

   const customers = computed<Customer[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      paginatedData,
      customers,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch single customer details
 */
export function useApiFetchCustomer(id: Ref<number>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/customer', {
      immediate: false,
      server: false,
      query: {
         id,
      },
   });

   const customer = computed<Customer | null>(() => {
      if (!error.value && data.value) {
         return CustomerSchema.parse(data.value);
      }
      return null;
   });

   return {
      customer,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

export interface FetchCustomerPawnsQueryParams extends PawningSortData {
   customer_id: string | number;
   page: number;
   pawn_id: number;
   pawn_date_min: string;
   pawn_date_max: string;
   pledge_date_min: string;
   pledge_date_max: string;
   collected_date_min: string;
   collected_date_max: string;
   weight_min?: number;
   weight_max?: number;
   state: 'ACTIVE' | 'COLLECTED' | 'EXPIRED' | null;
}

/**
 * Fetch all the pawns for a customer
 */
export function useApiFetchCustomerPawns(query: FetchCustomerPawnsQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/customer/pawns', {
      immediate: false,
      server: false,
      query,
   });

   const paginatedData = computed<PaginatedPawns | null>(() => {
      if (!error.value && data.value) {
         return PaginatedPawnsSchema.parse(data.value);
      }
      return null;
   });

   const pawns = computed<Pawn[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      error,
      status,
      paginatedData,
      pawns,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

export interface FetchCustomerBillsQueryParams {
   customer_id: string | number;
   page: number;
}

/**
 * Fetch all the bills for a customer
 */
export function useApiFetchCustomerBills(query: FetchCustomerBillsQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/customer/bills', {
      immediate: false,
      server: false,
      query,
   });

   const paginatedData = computed<PaginatedBills | null>(() => {
      if (!error.value && data.value) {
         return PaginatedBillsSchema.parse(data.value);
      }
      return null;
   });

   const bills = computed<Bill[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      error,
      status,
      paginatedData,
      bills,
      fetch,
   };
}
