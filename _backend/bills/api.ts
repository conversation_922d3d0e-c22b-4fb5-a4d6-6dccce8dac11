import type {
   Bill,
   FetchBillQueryParams,
   FetchBillsQueryParams,
   PaginatedBills,
   UpdateBillBody,
} from '~~/_backend/bills/types';
import { BillSchema, PaginatedBillsSchema } from '~~/_backend/bills/types';

/* ---------------------------------------------------------------------------------------------- */

/**
 * API: Fetch all bills
 */
export function useApiFetchBills(queryParams: FetchBillsQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/bills', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<PaginatedBills | null>(() => {
      if (!error.value && data.value) {
         return PaginatedBillsSchema.parse(data.value);
      }
      return null;
   });

   const bills = computed<Bill[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   const billsSummary = computed(() => {
      if (paginatedData.value) {
         return paginatedData.value.summary;
      }
   });

   return { data, error, status, fetch, paginatedData, bills, billsSummary };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch single bill data
 */
export function useApiFetchBill(queryParams: FetchBillQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/bill', {
      immediate: false,
      // server: false,
      query: queryParams,
   });

   const bill = computed<Bill | null>(() => {
      if (!error.value && data.value) {
         return BillSchema.parse(data.value);
      }
      return null;
   });

   return { bill, error, status, fetch };
}

/* ---------------------------------------------------------------------------------------------- */
