import { z } from 'zod';
import { CustomerSchema } from '~~/_backend/customers/types';
import { PaginatedItemsSchema } from '~~/_backend/types';
import { JewelrySchema } from '~~/_backend/jewelries/types';

/* ---------------------------------------------------------------------------------------------- */

export interface BillSortData {
   sort_by: 'id' | 'bill_date' | 'customer_id' | 'billed_by';
   sort_order: 'asc' | 'desc';
}

export interface FetchBillsQueryParams extends BillSortData {
   bill_id: number;
   page: number;
   date_min: string | null;
   date_max: string | null;
   state: 'OPEN' | 'CLOSE' | null;
}

export interface FetchBillQueryParams {
   id: number | string;
}

export interface CreateBillBody {
   customer_id: number | string;
   bill_date: string;
   billed_by: string;
   description: string;
}

export interface UpdateBillBody {
   id: number | string;
   discount: number;
   tax: number;
   state: 'OPEN' | 'CLOSE';
}

/* ---------------------------------------------------------------------------------------------- */

export const BillsStatsSchema = z.object({
   count: z.number(),
   total_cost: z.number(),
   total_sale: z.number(),
   total_price: z.number(),
   total_weight: z.number(),
   total_discount: z.number(),
   total_tax: z.number(),
});

export type BillsStats = z.infer<typeof BillsStatsSchema>;

export const BillItemSchema = z.object({
   id: z.union([z.string(), z.number()]),
   bill_id: z.union([z.string(), z.number()]),
   jewelry_id: z.union([z.string(), z.number()]),
   jewelry: JewelrySchema,
});

export const BillSchema = z.object({
   id: z.union([z.string(), z.number()]),
   customer_id: z.union([z.string(), z.number()]),
   bill_date: z.string(),
   description: z.string(),
   discount: z.number(),
   tax: z.number(),
   sub_total: z.number().optional(),
   bill_total: z.number().optional(),
   billed_by: z.string(),
   bill_items_total: z.number(),
   bill_items_cost: z.number(),
   bill_items_price: z.number(),
   bill_items_weight: z.number(),
   state: z.enum(['OPEN', 'CLOSE']),
   customer: CustomerSchema,
   bill_items: z.array(BillItemSchema),
});

export const PaginatedBillsSchema = PaginatedItemsSchema.extend({
   data: z.array(BillSchema),
   summary: z.object({
      closed_count: z.number(),
      open_count: z.number(),
      total_bill_amount: z.number(),
      total_weight: z.number(),
      total_cost: z.number(),
   }),
});

/* ---------------------------------------------------------------------------------------------- */

export type PaginatedBills = z.infer<typeof PaginatedBillsSchema>;
export type Bill = z.infer<typeof BillSchema>;
export type BillItem = z.infer<typeof BillItemSchema>;
