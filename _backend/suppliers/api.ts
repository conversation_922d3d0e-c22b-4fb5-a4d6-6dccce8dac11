import type {
   FetchSupplierJewelriesQueryParams,
   PaginatedSuppliers,
   Supplier,
} from '~~/_backend/suppliers/types';
import {
   PaginatedSuppliersSchema,
   SupplierListSchema,
   SupplierSchema,
} from '~~/_backend/suppliers/types';
import type { Jewellery, PaginatedJewelries } from '~~/_backend/jewelries/types';
import { PaginatedJewelriesSchema } from '~~/_backend/jewelries/types';
import type { Customer } from '~~/_backend/customers/types';
import { CustomerListSchema } from '~~/_backend/customers/types';

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch all suppliers
 */
export function useApiFetchSuppliers(queryParams: any) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/suppliers', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedSuppliers = computed<PaginatedSuppliers | null>(() => {
      if (!error.value && data.value) {
         return PaginatedSuppliersSchema.parse(data.value);
      }
      return null;
   });

   const suppliers = computed(() => {
      if (paginatedSuppliers.value && paginatedSuppliers.value.data) {
         return paginatedSuppliers.value.data;
      }
      return [];
   });

   return {
      data,
      paginatedSuppliers,
      suppliers,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch single supplier data
 */
export function useApiFetchSupplier(supplierId: MaybeRef<number>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/supplier', {
      immediate: false,
      server: false,
      query: {
         id: toValue(supplierId),
      },
   });

   const supplier = computed<Supplier | null>(() => {
      if (!error.value && data.value) {
         return SupplierSchema.parse(data.value);
      }
      return null;
   });

   return {
      supplier,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

export function useApiFetchSupplierJewelries(queryParams: FetchSupplierJewelriesQueryParams) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/supplier/jewelries', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<PaginatedJewelries | null>(() => {
      if (!error.value && data.value) {
         return PaginatedJewelriesSchema.parse(data.value);
      }
      return null;
   });

   const jewelries = computed<Jewellery[]>(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      paginatedData,
      jewelries,
      error,
      fetch,
      status,
   };
}

/* ---------------------------------------------------------------------------------------------- */

export function useApiSearchSuppliers(query: Ref<string>) {
   const {
      data,
      error,
      status,
      execute: fetch,
   } = useApiFetch('/suppliers/search', {
      immediate: false,
      server: false,
      query: {
         query,
      },
   });

   const suppliers = computed<Supplier[]>(() => {
      if (!error.value && data.value) {
         return SupplierListSchema.parse(data.value);
      }
      return [];
   });

   return {
      suppliers,
      error,
      fetch,
      status,
   };
}
