import { z } from 'zod';
import { PaginatedItemsSchema } from '~~/_backend/types';
import { JewelrySchema } from '~~/_backend/jewelries/types';

/* ---------------------------------------------------------------------------------------------- */

export interface FetchSupplierJewelriesQueryParams {
   page: number;
   supplier_id: number;
}

/* ---------------------------------------------------------------------------------------------- */

export const SupplierSchema = z.object({
   id: z.number(),
   supplier_name: z.string(),
   address: z.string(),
   phone: z.string(),
   email: z.string(),
   mobile: z.string(),
});

export const SupplierListSchema = z.array(SupplierSchema);

export const PaginatedSuppliersSchema = PaginatedItemsSchema.extend({
   data: z.array(SupplierSchema),
});

/* ---------------------------------------------------------------------------------------------- */

export type PaginatedSuppliers = z.infer<typeof PaginatedSuppliersSchema>;
export type Supplier = z.infer<typeof SupplierSchema>;
