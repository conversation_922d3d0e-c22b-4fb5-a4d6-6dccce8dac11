import type { LedgerEntry } from '~~/_backend/ledger/types';
import { LedgerEntryListSchema } from '~~/_backend/ledger/types';

export interface LedgerEntryQueryParams {
   entry_date_min: string;
   entry_date_max: string;
   query: string;
}

export function useApiFetchLedgerEntries(queryParams: LedgerEntryQueryParams) {
   const { data, error, execute: fetch, status } = useApiFetch('/ledger-entries', {
      query: queryParams,
      immediate: false,
      server: false,
   });

   const ledgerEntries = computed<LedgerEntry[]>(() => {
      if (!error.value && data.value) {
         return LedgerEntryListSchema.parse(data.value);
      }

      return [];
   });

   return {
      data,
      ledgerEntries,
      error,
      fetch,
      status,
   };
}
