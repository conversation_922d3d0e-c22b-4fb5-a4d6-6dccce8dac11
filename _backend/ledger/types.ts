import { z } from 'zod';

/* ---------------------------------------------------------------------------------------------- */

export const LedgerEntrySchema = z.object({
   id: z.union([z.number(), z.string()]),
   title: z.string(),
   entry_date: z.string().date(),
   entry_type: z.enum(['OPENING', 'DEBIT', 'CREDIT', 'CLOSING']),
   amount: z.number(),
   weight: z.number().nullable(),
});

export const LedgerEntryListSchema = z.array(LedgerEntrySchema);

export type LedgerEntry = z.infer<typeof LedgerEntrySchema>;
