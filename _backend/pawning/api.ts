import type { PaginatedPawns, Pawn, PawnStats, SimplePaginatedPawns } from '~~/_backend/pawning/types';
import { PaginatedPawnsSchema, PawnSchema, PawnsStatsSchema, SimplePaginatedPawnsSchema } from '~~/_backend/pawning/types';

export interface PawningSortData {
   sort_by: 'id' | 'customer_id' | 'value' | 'weight' | 'amount' | 'pawn_date' | 'pledge_date' | 'state';
   sort_order: 'asc' | 'desc';
}

export interface FetchPawningQueryParams extends PawningSortData {
   page: number;
   pawn_id: number;
   pawn_date_min: string;
   pawn_date_max: string;
   pledge_date_min: string;
   pledge_date_max: string;
   collected_date_min: string;
   collected_date_max: string;
   weight_min?: number;
   weight_max?: number;
   state: 'ACTIVE' | 'COLLECTED' | 'EXPIRED' | null;

}

/**
 * Fetch all pawns
 */
export function useApiFetchPawns(queryParams: FetchPawningQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/pawns', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<PaginatedPawns | null>(() => {
      if (!error.value && data.value) {
         return PaginatedPawnsSchema.parse(data.value);
      }

      return null;
   });

   const pawns = computed(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      paginatedData,
      pawns,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch single pawn details
 */
export function useApiFetchPawn(pawnId: Ref<number>) {
   const { data, error, status, execute: fetch } = useApiFetch('/pawn', {
      immediate: false,
      server: false,
      watch: false,
      query: {
         id: pawnId,
      },
   });

   const pawn = computed<Pawn | null>(() => {
      if (!error.value && data.value) {
         return PawnSchema.parse(data.value);
      }
      return null;
   });

   return {
      pawn,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch pawning stats
 */
export function useApiFetchPawningStats(minDate: Ref<string | null>, maxDate: Ref<string | null>) {
   const query = computed(() => {
      return {
         date_min: minDate.value,
         date_max: maxDate.value,
      };
   });

   const { data, error, status, execute: fetch } = useApiFetch('/pawns/stats', {
      immediate: false,
      server: false,
      query,
   });

   const pawningStats = computed<PawnStats | null>(() => {
      if (!error.value && data.value) {
         return PawnsStatsSchema.parse(data.value);
      }
      return null;
   });

   return {
      pawningStats,
      error,
      status,
      fetch,
   };
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Fetch unmarked pawns
 */
export interface FetchUnmarkedPawnsQueryParams {
   page: number;
   pledge_date_min?: string;
   sort_by: 'id' | 'customer_id' | 'value' | 'weight' | 'amount' | 'pawn_date' | 'pledge_date' | 'state';
   sort_order: 'asc' | 'desc';
}

export function useApiFetchUnmarkedPawns(queryParams: FetchUnmarkedPawnsQueryParams) {
   const { data, error, status, execute: fetch } = useApiFetch('/pawns/unmarked', {
      immediate: false,
      server: false,
      query: queryParams,
   });

   const paginatedData = computed<SimplePaginatedPawns | null>(() => {
      if (!error.value && data.value) {
         return SimplePaginatedPawnsSchema.parse(data.value);
      }
      return null;
   });

   const pawns = computed(() => {
      if (paginatedData.value) {
         return paginatedData.value.data;
      }
      return [];
   });

   return {
      paginatedData,
      pawns,
      error,
      status,
      fetch,
   };
}
