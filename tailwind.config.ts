import type { Config } from 'tailwindcss';

export default <Partial<Config>>{
   safelist: ['text-right', 'placeholder:text-left'],
   theme: {

      fontFamily: {
         sans: ['Geist'],
         code: ['Comic Code', 'monospace'],
      },

      extend: {
         // fontSize: {
         //    base: '14px',
         //    sm: '14px',
         //    xs: '12px',
         // },
         colors: {
            primaryColor: {
               50: '#eff6ff',
               100: '#dbeafe',
               200: '#bfdbfe',
               300: '#93c5fd',
               400: '#60a5fa',
               500: '#3b82f6',
               600: '#2563eb',
               700: '#1d4ed8',
               800: '#1e40af',
               900: '#1e3a8a',
               950: '#172554',
            },
            secondaryColor: {
               50: '#f9fafb',
               100: '#f3f4f6',
               200: '#e5e7eb',
               300: '#d1d5db',
               400: '#9ca3af',
               500: '#6b7280',
               600: '#4b5563',
               700: '#374151',
               800: '#1f2937',
               900: '#111827',
               950: '#030712',
            },
            black: '#232C35',
         },
         boxShadow: {
            DEFAULT: '0px 0px 20px 5px #e2e8f0',
         },
      },
   },
};
