{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify-json/fa6-solid": "^1.2.2", "@iconify-json/uil": "^1.2.0", "@nuxt/image": "^1.8.0", "@nuxt/ui": "^2.18.4", "@nuxtjs/google-fonts": "^3.2.0", "@pinia/nuxt": "^0.5.3", "@vuepic/vue-datepicker": "^10.0.0", "@vueuse/core": "^11.0.0", "@vueuse/nuxt": "^11.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "dayjs-nuxt": "^2.1.9", "es-toolkit": "^1.17.0", "nuxt-auth-utils": "^0.3.4", "tailwindcss": "^3.4.10", "v-calendar": "^3.1.2", "vue": "latest", "zod": "^3.23.8"}, "packageManager": "pnpm@9.7.1+sha512.faf344af2d6ca65c4c5c8c2224ea77a81a5e8859cbc4e06b1511ddce2f0151512431dd19e6aff31f2c6a8f5f2aced9bd2273e1fed7dd4de1868984059d2c4247", "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}, "devDependencies": {"@antfu/eslint-config": "^2.26.0", "eslint": "9.5.0", "eslint-plugin-format": "^0.1.2", "nuxt": "^3.13.2", "prettier": "^3.3.3", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-tailwindcss": "^0.6.6"}}