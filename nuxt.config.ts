// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
   future: {
      compatibilityVersion: 4,
   },

   compatibilityDate: '2024-04-03',
   devtools: { enabled: false },
   modules: [
      'nuxt-auth-utils',
      '@nuxt/ui',
      '@pinia/nuxt',
      '@vueuse/nuxt',
      '@nuxtjs/google-fonts',
      'dayjs-nuxt',
      '@nuxt/image',
   ],

   /*
    * Components
    */
   components: [
      { path: '~/layout', pathPrefix: false },
      { path: '~/components', pathPrefix: false },
      { path: '~/page-components', pathPrefix: false },
   ],

   // nuxtUi colorMode
   colorMode: {
      preference: 'light',
   },

   /*
    * Fonts options
    */
   googleFonts: {
      families: {
         Geist: [400, 500, 600, 700, 800],
      },
      prefetch: true,
      preconnect: true,
   },

   runtimeConfig: {
      // SESSION_PASSWORD: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6A7B8C9D0E1F2G3H4',
      session: {
         maxAge: 60 * 60 * 24 * 7,
      },

      public: {
         apiUrl: '',
         interestRateFirstWeek: '',
         interestRateSecondWeek: '',
         interestRateThirdWeek: '',
         interestRateFourthWeek: '',
      },
   },

   icon: {
      clientBundle: {
         scan: true,
         sizeLimitKb: 256,
      },
   },

   build: {
      transpile: ['@vuepic/vue-datepicker'],
   },
});
