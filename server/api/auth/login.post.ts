import { z } from 'zod';
import { FetchError } from 'ofetch';
import { getApiBaseURL } from '~/utils/api-utils';

/* ---------------------------------------------------------------------------------------------- */

const bodySchema = z.object({
   email: z.string().email(),
   password: z.string(),
});

const responseSchema = z.object({
   user: z.object({
      id: z.number(),
      email: z.string().email(),
      full_name: z.string(),
      role: z.enum(['ADMIN', 'USER', 'MANAGER']),
      profile_pic: z.string(),
   }),
   token: z.string(),
});

/* ---------------------------------------------------------------------------------------------- */

/**
 * Handles login and then create a valid session
 */
export default defineEventHandler(async (event) => {
   const bodyResult = await readValidatedBody(event, body => bodySchema.safeParse(body));

   if (bodyResult.error) {
      throw createError({
         statusCode: 422,
         message: bodyResult.error.message,
      });
   }

   const baseURL = getApiBaseURL();

   try {
      const response = await $fetch(`${baseURL}/login`, {
         method: 'POST',
         body: {
            email: bodyResult.data.email,
            password: bodyResult.data.password,
         },
      });

      const responseData = responseSchema.parse(response);

      await setUserSession(event, {
         user: {
            id: responseData.user.id,
            email: responseData.user.email,
            fullName: responseData.user.full_name,
            role: responseData.user.role,
            profilePicture: responseData.user.profile_pic,
            isAdmin: responseData.user.role === 'ADMIN',
         },
         loggedInAt: new Date(),
         token: responseData.token,
      });

      return {
         message: 'Login complete',
      };
   }
   catch (e) {
      console.error(e);

      if (e instanceof FetchError) {
         throw createError({
            statusCode: 400,
            message: e.data.error,
         });
      }

      throw createError({
         statusCode: 401,
         message: 'Failed to authenticate',
      });
   }
});
